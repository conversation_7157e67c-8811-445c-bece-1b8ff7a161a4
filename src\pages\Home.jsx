import React, { useEffect, useMemo, useState } from 'react'
import { Link } from 'react-router-dom'
import {
  Mi<PERSON>,
  Brain,
  MessageCircle,
  FileText,
  Zap,
  ArrowRight,
<<<<<<< HEAD
  CheckCircle,
  Sparkles,
  Users,
  Clock,
  Award,
  BookOpen,
  Rocket,
  Shield,
  Globe
=======
  BarChart3,
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
} from 'lucide-react'

// Small CountUp component for animated stats
const CountUp = ({ end = 0, duration = 1600, formatter = (n) => n.toLocaleString(), suffix = '+' }) => {
  const [value, setValue] = useState(0)

  useEffect(() => {
    let raf
    const start = performance.now()

    const step = (now) => {
      const progress = Math.min((now - start) / duration, 1)
      const eased = 1 - Math.pow(1 - progress, 3) // easeOutCubic
      setValue(Math.floor(eased * end))
      if (progress < 1) raf = requestAnimationFrame(step)
    }

    raf = requestAnimationFrame(step)
    return () => cancelAnimationFrame(raf)
  }, [end, duration])

  return (
    <span>
      {formatter(value)}{suffix}
    </span>
  )
}

const Home = () => {
  const features = useMemo(() => ([
    {
<<<<<<< HEAD
      icon: Mic,
      title: 'AI Voice Interviews',
      description: 'Practice with advanced AI that adapts to your responses and provides real-time insights',
      path: '/hr-interview',
      gradient: 'from-blue-500 via-blue-600 to-cyan-500',
      stats: `${user.stats.hrInterviewsCompleted} completed`,
      badge: 'Popular',
      delay: 'delay-0'
    },
    {
      icon: Brain,
      title: 'Smart Aptitude Tests',
      description: 'Adaptive testing that focuses on your weak areas with personalized improvement plans',
      path: '/aptitude-quiz',
      gradient: 'from-purple-500 via-violet-600 to-pink-500',
      stats: `${user.stats.aptitudeQuizzesTaken} taken`,
      badge: 'Updated',
      delay: 'delay-100'
    },
    {
      icon: Code,
      title: 'Live Coding Arena',
      description: 'Real-time code execution with AI-powered hints and optimization suggestions',
      path: '/coding-challenge',
      gradient: 'from-green-500 via-emerald-600 to-teal-500',
      stats: `${user.stats.codingChallengesSolved} solved`,
      badge: 'Pro',
      delay: 'delay-200'
    },
    {
      icon: MessageCircle,
      title: 'AI Mentor Chat',
      description: 'Your personal interview coach available 24/7 for instant guidance and tips',
      path: '/ai-doubt-solver',
      gradient: 'from-orange-500 via-red-500 to-rose-500',
      stats: 'Always available',
      badge: 'New',
      delay: 'delay-300'
=======
      icon: FileText,
      title: 'Resume Analyzer',
      description: 'Instant, AI-powered insights to improve your resume and boost shortlisting chances.',
      path: '/resume-analyzer',
      gradient: 'from-cyan-500 via-blue-500 to-fuchsia-600'
    },
    {
      icon: Brain,
      title: 'Aptitude Practice',
      description: 'Timed quizzes with smart feedback to sharpen quantitative, logical, and verbal skills.',
      path: '/aptitude-quiz',
      gradient: 'from-purple-500 via-fuchsia-500 to-pink-500'
    },
    {
      icon: Mic,
      title: 'HR Interview Prep',
      description: 'Simulated interviews with structured feedback to improve clarity and confidence.',
      path: '/hr-interview',
      gradient: 'from-blue-500 via-cyan-500 to-emerald-500'
    },
    {
      icon: MessageCircle,
      title: 'AI Doubt Assistant',
      description: 'Ask anything. Learn concepts and solve problems with a chat-based AI mentor.',
      path: '/ai-doubt-solver',
      gradient: 'from-amber-500 via-orange-500 to-rose-500'
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
    }
  ]), [])

<<<<<<< HEAD
  const achievements = [
    { 
      icon: Target, 
      title: 'First Steps', 
      description: 'Complete your first practice session',
      unlocked: user.stats.hrInterviewsCompleted > 0 || user.stats.aptitudeQuizzesTaken > 0 || user.stats.codingChallengesSolved > 0
    },
    { 
      icon: TrendingUp, 
      title: 'Rising Star', 
      description: 'Score above 80% in any category',
      unlocked: user.stats.averageScore > 80
    },
    { 
      icon: Star, 
      title: 'Expert Level', 
      description: 'Complete all practice modules',
      unlocked: user.progress.hrInterview > 90 && user.progress.aptitudeQuiz > 90 && user.progress.codingChallenge > 90
    },
    { 
      icon: Trophy, 
      title: 'Champion', 
      description: 'Reach top 10 on leaderboard',
      unlocked: user.achievements.includes('Champion')
    }
  ]

  const stats = [
    {
      value: user.stats.hrInterviewsCompleted + user.stats.aptitudeQuizzesTaken + user.stats.codingChallengesSolved,
      label: 'Practice Sessions',
      icon: Clock,
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      value: `${Math.round(user.stats.averageScore)}%`,
      label: 'Average Score',
      icon: Target,
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      value: user.achievements.length,
      label: 'Achievements',
      icon: Trophy,
      gradient: 'from-yellow-500 to-orange-500'
    },
    {
      value: Math.round((user.progress.hrInterview + user.progress.aptitudeQuiz + user.progress.codingChallenge) / 3),
      suffix: '%',
      label: 'Overall Progress',
      icon: TrendingUp,
      gradient: 'from-purple-500 to-pink-500'
    }
  ]

  const testimonials = [
    {
      quote: "Crackly helped me land my dream job at Google. The AI feedback was incredibly accurate!",
      author: "Sarah Chen",
      role: "Software Engineer at Google",
      avatar: "SC"
    },
    {
      quote: "The coding challenges are exactly what I faced in real interviews. 10/10 would recommend.",
      author: "Michael Rodriguez",
      role: "Full Stack Developer",
      avatar: "MR"
    },
    {
      quote: "Best interview prep platform I've used. The voice practice feature is game-changing.",
      author: "Priya Sharma",
      role: "Product Manager",
      avatar: "PS"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Hero Section with Glassmorphism */}
      <section className="relative overflow-hidden min-h-screen flex items-center">
        {/* Animated Background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-600">
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
          </div>
          {/* Floating Orbs */}
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-cyan-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 z-10">
          <div className="text-center">
            {/* Logo with Enhanced Animation */}
            <div className="flex justify-center mb-12">
              <div className="relative group">
                <div className="absolute -inset-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full blur-xl opacity-30 group-hover:opacity-50 animate-pulse"></div>
                <div className="relative w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-xl border border-white/30 shadow-2xl group-hover:scale-110 transition-all duration-500">
                  <Zap className="w-12 h-12 text-white group-hover:rotate-12 transition-transform duration-500" />
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-bounce">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-8 mb-12">
              <h1 className="text-6xl md:text-8xl font-black leading-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-gray-100 to-white">
                  Welcome to{' '}
                </span>
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse">
                  Crackly
                </span>
              </h1>
              
              <p className="text-xl md:text-3xl text-white/90 max-w-5xl mx-auto leading-relaxed font-light">
                Transform your interview preparation with{' '}
                <span className="font-semibold text-yellow-300">AI-powered practice sessions</span>, 
                real-time feedback, and personalized learning paths designed by industry experts.
              </p>
            </div>
            
            <div className="flex flex-col lg:flex-row gap-6 justify-center items-center mb-16">
              <Link 
                to="/hr-interview" 
                className="group relative bg-white text-gray-900 hover:bg-gray-50 px-12 py-6 text-xl font-bold rounded-2xl flex items-center space-x-4 shadow-2xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <Rocket className="w-6 h-6 group-hover:rotate-12 transition-transform duration-300" />
                <span>Start Your Journey</span>
                <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
                <div className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center animate-bounce">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
              </Link>
              
              <Link 
                to="/profile" 
                className="group relative border-2 border-white/50 text-white hover:bg-white/10 hover:border-white px-12 py-6 text-xl font-semibold rounded-2xl backdrop-blur-sm transition-all duration-300 flex items-center space-x-3 overflow-hidden"
              >
                <div className="absolute inset-0 bg-white/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                <TrendingUp className="w-6 h-6 group-hover:scale-110 transition-transform duration-300" />
                <span>View Progress</span>
              </Link>
            </div>
            
            <div className="flex flex-col items-center space-y-4 text-white/70">
              <div className="flex items-center space-x-8">
                <div className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-sm font-medium">Free to start</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-blue-400" />
                  <span className="text-sm font-medium">50K+ users</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="w-5 h-5 text-purple-400" />
                  <span className="text-sm font-medium">Available worldwide</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section with Enhanced Design */}
      <section className="py-24 bg-white dark:bg-gray-800 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-purple-50/30 to-pink-50/50 dark:from-gray-800 dark:to-gray-800"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={index} 
                className="group text-center p-8 rounded-3xl bg-white/80 dark:bg-gray-700/80 backdrop-blur-xl border border-gray-200/50 dark:border-gray-600/50 shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 transform"
              >
                <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${stat.gradient} mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-xl`}>
                  <stat.icon className="w-10 h-10 text-white" />
                </div>
                <div className="text-4xl lg:text-5xl font-black text-gray-900 dark:text-white mb-3 group-hover:scale-105 transition-transform duration-300">
                  {stat.value}{stat.suffix || ''}
                </div>
                <div className="text-gray-600 dark:text-gray-400 font-semibold text-lg">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section with Enhanced Design */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-600">
            <div className="absolute inset-0 bg-black/30"></div>
          </div>
          {/* Animated Background Elements */}
          <div className="absolute top-0 left-0 w-full h-full">
            <div className="absolute top-20 left-10 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-80 h-80 bg-yellow-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-cyan-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
          </div>
        </div>
        
        <div className="relative max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center z-10">
          <div className="mb-12">
            <div className="inline-flex items-center px-6 py-3 bg-white/20 text-white rounded-full text-sm font-bold backdrop-blur-sm shadow-xl mb-8">
              <Sparkles className="w-5 h-5 mr-3 animate-spin" />
              Start Your Success Story
            </div>
            
            <h2 className="text-5xl md:text-7xl font-black mb-10 text-white leading-tight">
              Ready to Crack Your <br />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 animate-pulse">
                Dream Interview?
              </span>
            </h2>
            
            <p className="text-xl md:text-2xl mb-12 text-white/90 max-w-4xl mx-auto leading-relaxed">
              Join over 50,000 successful candidates who transformed their career prospects 
              with Crackly's AI-powered interview preparation system.
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-8 justify-center items-center mb-16">
            <Link 
              to="/hr-interview" 
              className="group relative bg-white text-gray-900 hover:bg-gray-50 px-12 py-6 text-xl font-bold rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl flex items-center space-x-4 overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Mic className="w-7 h-7 group-hover:rotate-12 transition-transform duration-300" />
              <span>Start HR Practice</span>
              <ArrowRight className="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" />
            </Link>
            
            <Link 
              to="/coding-challenge" 
              className="group relative border-2 border-white/50 text-white hover:bg-white/10 hover:border-white px-12 py-6 text-xl font-semibold rounded-2xl backdrop-blur-sm transition-all duration-300 flex items-center space-x-4 overflow-hidden"
            >
              <div className="absolute inset-0 bg-white/5 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              <Code className="w-7 h-7 group-hover:scale-110 transition-transform duration-300" />
              <span>Try Coding Challenge</span>
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto text-white/80">
            <div className="flex flex-col items-center space-y-3 p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 group">
              <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
              <span className="text-lg font-semibold">Free to start</span>
              <span className="text-sm text-white/60">No credit card required</span>
            </div>
            
            <div className="flex flex-col items-center space-y-3 p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 group">
              <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Zap className="w-6 h-6 text-blue-400" />
              </div>
              <span className="text-lg font-semibold">Instant feedback</span>
              <span className="text-sm text-white/60">Real-time AI analysis</span>
            </div>
            
            <div className="flex flex-col items-center space-y-3 p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300 group">
              <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
              <span className="text-lg font-semibold">50K+ users</span>
              <span className="text-sm text-white/60">Trusted worldwide</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="py-20 bg-gray-900 dark:bg-gray-950">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center mb-8">
            <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-xl">
              <Zap className="w-8 h-8 text-white" />
            </div>
          </div>
          
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Your interview success starts here
          </h3>
          
          <p className="text-xl text-gray-400 mb-8">
            Don't let another opportunity slip away. Start practicing today and land your dream job tomorrow.
          </p>
          
          <Link 
            to="/signup"
            className="inline-flex items-center bg-gradient-to-r from-yellow-400 to-orange-500 text-gray-900 hover:from-yellow-300 hover:to-orange-400 px-10 py-4 text-lg font-bold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-xl"
          >
            <BookOpen className="w-6 h-6 mr-3" />
            Get Started Free
            <ArrowRight className="w-5 h-5 ml-3" />
          </Link>
        </div>
      </section>
    </div>
  )
}

export default Home

      {/* Features Section with Staggered Animation */}
      <section className="py-32 bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-400/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-24">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm font-bold mb-8 shadow-lg">
              <Sparkles className="w-5 h-5 mr-3" />
              AI-Powered Features
            </div>
            <h2 className="text-5xl md:text-7xl font-black text-gray-900 dark:text-white mb-8 leading-tight">
              Master Every <br />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">
                Interview Challenge
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-400 max-w-4xl mx-auto leading-relaxed">
              Experience the future of interview preparation with our cutting-edge AI technology 
              that adapts to your learning style and accelerates your success.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-10">
            {features.map((feature, index) => (
              <Link
                key={index}
                to={feature.path}
                className={`group relative bg-white dark:bg-gray-800 rounded-3xl p-10 shadow-xl hover:shadow-2xl border border-gray-200/50 dark:border-gray-700/50 transition-all duration-700 hover:-translate-y-3 overflow-hidden ${feature.delay}`}
              >
                {/* Background Gradient Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
                
                {/* Animated Border */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-500`}></div>
                
                {/* Badge */}
                <div className="absolute top-8 right-8">
                  <span className={`px-4 py-2 text-xs font-bold rounded-full bg-gradient-to-r ${feature.gradient} text-white shadow-xl transform group-hover:scale-110 transition-transform duration-300`}>
                    {feature.badge}
                  </span>
                </div>
                
                <div className="relative">
                  <div className={`inline-flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br ${feature.gradient} mb-8 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl`}>
                    <feature.icon className="w-10 h-10 text-white" />
                  </div>
                  
                  <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-6 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-gray-900 group-hover:to-gray-600 dark:group-hover:from-white dark:group-hover:to-gray-300 transition-all duration-300">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-8 leading-relaxed text-lg">
                    {feature.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className={`text-sm font-bold bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}>
                      {feature.stats}
                    </div>
                    <div className="flex items-center space-x-2 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-300">
                      <span className="text-sm font-semibold">Get Started</span>
                      <ArrowRight className="w-5 h-5 group-hover:translate-x-2 transition-transform duration-300" />
                    </div>
                  </div>
=======
  const stats = useMemo(() => ([
    { icon: FileText, label: 'Resumes Analysed', value: 12000 },
    { icon: Brain, label: 'Aptitude Tests Taken', value: 8500 },
    { icon: Mic, label: 'Interviews Prepared', value: 4000 },
  ]), [])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-gray-950 to-black text-white">
        {/* Background accents */}
        <div aria-hidden className="pointer-events-none absolute inset-0 -z-10">
          <div className="absolute -top-40 -left-32 h-96 w-96 rounded-full bg-gradient-to-tr from-cyan-500/25 to-fuchsia-500/25 blur-3xl" />
          <div className="absolute -bottom-40 -right-32 h-[28rem] w-[28rem] rounded-full bg-gradient-to-tr from-blue-500/20 to-cyan-500/20 blur-3xl" />
          <div className="absolute inset-0 opacity-[0.06] bg-[radial-gradient(ellipse_at_top,rgba(255,255,255,0.15),rgba(0,0,0,0)_60%)]" />
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 md:py-28">
          <div className="flex flex-col items-center text-center">
            <div className="mb-6 inline-flex items-center gap-2 rounded-full border border-white/10 bg-white/5 px-3 py-1 text-xs text-white/80 backdrop-blur-sm">
              <Zap className="h-3.5 w-3.5 text-cyan-300" />
              <span>AI Career Suite</span>
            </div>

            <h1 className="text-4xl md:text-6xl font-extrabold tracking-tight leading-tight">
              Ace Your Career Journey with AI-Powered Tools
            </h1>
            <p className="mt-5 max-w-3xl text-lg md:text-xl text-white/80">
              Analyse your resume, test your aptitude, prepare for interviews, and get instant AI guidance.
            </p>

            <div className="mt-8 flex flex-col sm:flex-row gap-4">
              <Link
                to="/resume-analyzer"
                className="inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-cyan-400 via-blue-500 to-fuchsia-500 px-7 py-3 text-base font-semibold text-white shadow-[0_15px_40px_-10px_rgba(56,189,248,0.55)] transition-all hover:shadow-[0_20px_50px_-12px_rgba(168,85,247,0.55)] focus:outline-none focus-visible:ring-2 focus-visible:ring-cyan-400/60"
              >
                Get Started
                <ArrowRight className="h-5 w-5" />
              </Link>
              <Link
                to="/hr-interview"
                className="inline-flex items-center justify-center gap-2 rounded-xl border border-white/15 bg-white/5 px-7 py-3 text-base font-semibold text-white/90 backdrop-blur-sm transition-all hover:bg-white/10"
              >
                Explore Modules
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Feature Cards */}
      <section className="relative py-16 sm:py-20 bg-gradient-to-b from-black to-gray-950">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-3">Everything you need to crack your next step</h2>
            <p className="text-white/70 text-lg">Four focused tools. One seamless experience.</p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {features.map(({ icon: Icon, title, description, path, gradient }, idx) => (
              <Link
                key={title}
                to={path}
                className="group relative rounded-2xl border border-white/10 bg-white/5 p-6 backdrop-blur-md transition-all duration-300 hover:-translate-y-1.5 hover:shadow-[0_25px_60px_-15px_rgba(56,189,248,0.3)]"
              >
                <div className={`mb-5 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br ${gradient} text-white shadow-[0_10px_30px_-10px_rgba(56,189,248,0.55)] group-hover:scale-105 transition-transform`}>
                  <Icon className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
                <p className="text-sm text-white/70 leading-relaxed mb-5">{description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-cyan-300/90">Go</span>
                  <span className="inline-flex items-center gap-2 rounded-lg border border-white/10 bg-white/5 px-3 py-2 text-xs font-medium text-white/90 transition-all group-hover:translate-x-0.5">
                    Open <ArrowRight className="h-3.5 w-3.5" />
                  </span>
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
                </div>
                <div aria-hidden className="pointer-events-none absolute inset-0 rounded-2xl ring-1 ring-white/10 group-hover:ring-white/20" />
              </Link>
            ))}
          </div>
        </div>
      </section>

<<<<<<< HEAD
      {/* Progress Section with Enhanced Visuals */}
      <section className="py-32 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-black text-gray-900 dark:text-white mb-8">
              Track Your{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-emerald-600">
                Growth Journey
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Visualize your progress with detailed analytics and personalized insights
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            {[
              { 
                title: 'HR Interview', 
                icon: Mic, 
                progress: user.progress.hrInterview, 
                gradient: 'from-blue-500 to-cyan-500',
                description: 'Master behavioral questions and communication skills'
              },
              { 
                title: 'Aptitude Quiz', 
                icon: Brain, 
                progress: user.progress.aptitudeQuiz, 
                gradient: 'from-purple-500 to-pink-500',
                description: 'Sharpen logical reasoning and analytical thinking'
              },
              { 
                title: 'Coding Challenge', 
                icon: Code, 
                progress: user.progress.codingChallenge, 
                gradient: 'from-green-500 to-emerald-500',
                description: 'Build problem-solving and programming expertise'
              }
            ].map((item, index) => (
              <div key={index} className="group bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-3xl p-10 border border-gray-200/50 dark:border-gray-600/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
                
                <div className="relative">
                  <div className="flex items-center justify-between mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{item.title}</h3>
                    <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${item.gradient} flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-xl`}>
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-8 text-sm">{item.description}</p>
                  
                  <div className="relative mb-6">
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-6 shadow-inner">
                      <div 
                        className={`bg-gradient-to-r ${item.gradient} h-6 rounded-full transition-all duration-1000 shadow-lg relative overflow-hidden group-hover:shadow-xl`}
                        style={{ width: `${item.progress}%` }}
                      >
                        <div className="absolute inset-0 bg-white/30 animate-pulse rounded-full"></div>
                        <div className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-white rounded-full animate-ping`}></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-3xl font-bold text-gray-900 dark:text-white group-hover:scale-110 transition-transform duration-300">
                      {Math.round(item.progress)}%
                    </span>
                    <span className={`text-sm font-bold bg-gradient-to-r ${item.gradient} bg-clip-text text-transparent`}>
                      Complete
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-32 bg-gray-50 dark:bg-gray-900 relative overflow-hidden">
        <div className="absolute top-20 left-20 w-72 h-72 bg-yellow-400/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-orange-400/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-sm font-bold mb-8 shadow-lg">
              <Trophy className="w-5 h-5 mr-3" />
              Achievement System
            </div>
            <h2 className="text-5xl md:text-6xl font-black text-gray-900 dark:text-white mb-8">
              Unlock Your{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-500 to-orange-500">
                Potential
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Earn badges and celebrate milestones as you progress through your learning journey
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div 
                key={index} 
                className={`group relative bg-white dark:bg-gray-800 rounded-3xl p-8 text-center border transition-all duration-500 hover:-translate-y-3 transform ${
                  achievement.unlocked 
                    ? 'border-yellow-200 dark:border-yellow-800 shadow-xl hover:shadow-2xl' 
                    : 'border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl'
                }`}
              >
                {achievement.unlocked && (
                  <>
                    <div className="absolute -top-3 -right-3 w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-xl animate-bounce">
                      <CheckCircle className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/5 to-orange-500/5 rounded-3xl"></div>
                  </>
                )}
                
                <div className={`relative w-24 h-24 rounded-2xl mx-auto mb-8 flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-xl ${
                  achievement.unlocked 
                    ? 'bg-gradient-to-br from-yellow-400 to-orange-500' 
                    : 'bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-600 dark:to-gray-700'
                }`}>
                  <achievement.icon className={`w-12 h-12 ${achievement.unlocked ? 'text-white' : 'text-gray-500 dark:text-gray-400'}`} />
                  {!achievement.unlocked && (
                    <div className="absolute inset-0 bg-black/20 rounded-2xl backdrop-blur-sm"></div>
                  )}
                </div>
                
                <h3 className={`text-xl font-bold mb-4 ${
                  achievement.unlocked 
                    ? 'text-gray-900 dark:text-white' 
                    : 'text-gray-500 dark:text-gray-400'
                }`}>
                  {achievement.title}
                </h3>
                
                <p className={`text-sm leading-relaxed mb-6 ${
                  achievement.unlocked 
                    ? 'text-gray-600 dark:text-gray-300' 
                    : 'text-gray-400 dark:text-gray-500'
                }`}>
                  {achievement.description}
                </p>
                
                {achievement.unlocked ? (
                  <div className="px-4 py-2 bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-800 dark:text-yellow-300 rounded-full text-xs font-bold shadow-lg">
                    ✨ Unlocked!
                  </div>
                ) : (
                  <div className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-full text-xs font-semibold">
                    🔒 Locked
=======
      {/* Stats Section */}
      <section className="relative py-16 sm:py-20 bg-black">
        <div aria-hidden className="pointer-events-none absolute inset-0 -z-10">
          <div className="absolute left-0 right-0 top-0 mx-auto h-px w-full max-w-6xl bg-gradient-to-r from-transparent via-cyan-500/30 to-transparent" />
        </div>
        <div className="max-w-6xl mx-auto px-4 sm:px-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 md:gap-8">
            {stats.map(({ icon: Icon, label, value }) => (
              <div key={label} className="relative overflow-hidden rounded-2xl border border-white/10 bg-gradient-to-b from-white/5 to-white/[0.03] p-6 backdrop-blur-md">
                <div className="flex items-center gap-3 mb-2">
                  <div className="inline-flex h-10 w-10 items-center justify-center rounded-lg bg-white/7 text-cyan-300">
                    <Icon className="h-5 w-5" />
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
                  </div>
                  <span className="text-sm text-white/70">{label}</span>
                </div>
                <div className="flex items-end gap-2">
                  <div className="text-3xl md:text-4xl font-extrabold text-white">
                    <CountUp end={value} />
                  </div>
                  <BarChart3 className="h-5 w-5 text-cyan-300/80 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

<<<<<<< HEAD
      {/* Testimonials Section */}
      <section className="py-32 bg-white dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-black text-gray-900 dark:text-white mb-8">
              Success{' '}
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
                Stories
              </span>
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-400">
              Join thousands who transformed their careers with Crackly
            </p>
=======
      {/* CTA Section */}
      <section className="py-16 sm:py-20 bg-gradient-to-r from-cyan-500/15 via-blue-500/15 to-fuchsia-500/15">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 text-center">
          <h3 className="text-2xl md:text-3xl font-bold text-white">Build confidence. Go from unsure to interview-ready.</h3>
          <p className="mt-3 md:mt-4 text-white/80 text-lg">Designed for speed, clarity, and career growth — across devices.</p>
          <div className="mt-7">
            <Link
              to="/resume-analyzer"
              className="inline-flex items-center justify-center gap-2 rounded-xl bg-gradient-to-r from-cyan-400 via-blue-500 to-fuchsia-500 px-7 py-3 text-base font-semibold text-white shadow-[0_15px_40px_-10px_rgba(56,189,248,0.55)] transition-all hover:shadow-[0_20px_50px_-12px_rgba(168,85,247,0.55)] focus:outline-none focus-visible:ring-2 focus-visible:ring-cyan-400/60"
            >
              Get Started
              <ArrowRight className="h-5 w-5" />
            </Link>
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
          </div>

<<<<<<< HEAD
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-white dark:from-gray-700 dark:to-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200/50 dark:border-gray-600/50 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 group">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:scale-110 transition-transform duration-300">
                    {testimonial.avatar}
                  </div>
                  <div className="ml-4">
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white">{testimonial.author}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{testimonial.role}</p>
                  </div>
                </div>
                <blockquote className="text-gray-700 dark:text-gray-300 text-lg leading-relaxed italic">
                  "{testimonial.quote}"
                </blockquote>
                <div className="flex mt-6">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
              </div>
            ))}
            </div>
            </div>
            </section>
=======
export default Home
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
