# Firebase Setup Guide for Crackly

## 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: `crackly-interview-app`
4. Enable Google Analytics (optional)
5. Click "Create project"

## 2. Enable Authentication

1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable **Email/Password** provider
3. Click "Save"

## 3. Create Firestore Database

1. Go to **Firestore Database**
2. Click "Create database"
3. Choose "Start in test mode" (we'll update rules later)
4. Select a location (choose closest to your users)
5. Click "Done"

## 4. Set Up Firestore Security Rules

Go to **Firestore Database** > **Rules** and replace the content with:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow read access for leaderboard
      allow read: if request.auth != null;
    }
    
    // Interview sessions are private to users
    match /interviewSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.uid;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uid;
    }
    
    // Aptitude sessions are private to users
    match /aptitudeSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.uid;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uid;
    }
    
    // Coding sessions are private to users
    match /codingSessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.uid;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uid;
    }
    
    // Analytics are private to users
    match /analytics/{analyticsId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.uid;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uid;
    }
  }
}
```

Click **"Publish"** to save the rules.

## 5. Enable Storage (Optional)

1. Go to **Storage**
2. Click "Get started"
3. Choose "Start in test mode"
4. Select same location as Firestore
5. Click "Done"

## 6. Get Firebase Configuration

1. Go to **Project Settings** (gear icon)
2. Scroll down to "Your apps"
3. Click "Add app" > Web app icon `</>`
4. Enter app nickname: `crackly-web`
5. Check "Also set up Firebase Hosting" (optional)
6. Click "Register app"
7. Copy the configuration object

## 7. Update Environment Variables

Create/update your `.env` file with the Firebase config:

```env
# OpenAI Configuration
VITE_OPENAI_API_KEY=your_openai_api_key_here

# Firebase Configuration (replace with your actual values)
VITE_FIREBASE_API_KEY=AIzaSyC...
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=*********
VITE_FIREBASE_APP_ID=1:*********:web:abcdef
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEFGHIJ
```

## 8. Test the Setup

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Try to register a new account
3. Check if user data appears in Firestore
4. Test login/logout functionality

## 9. Storage Rules (if using file uploads)

Go to **Storage** > **Rules** and use:

```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Profile pictures
    match /profilePictures/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public files (if needed)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

## 10. Firestore Indexes (if needed)

If you get index errors, Firebase will provide links to create them automatically. Common indexes needed:

- Collection: `analytics`, Fields: `uid` (Ascending), `timestamp` (Descending)
- Collection: `interviewSessions`, Fields: `uid` (Ascending), `createdAt` (Descending)
- Collection: `aptitudeSessions`, Fields: `uid` (Ascending), `createdAt` (Descending)
- Collection: `codingSessions`, Fields: `uid` (Ascending), `createdAt` (Descending)

## Troubleshooting

### Common Issues:

1. **Rules Error**: Make sure there are no syntax errors in Firestore rules
2. **Permission Denied**: Check if user is authenticated and rules allow the operation
3. **CORS Errors**: Make sure you're using the correct domain in Firebase settings
4. **Index Errors**: Create composite indexes as suggested by Firebase

### Testing Rules:

You can test your rules in the Firebase Console:
1. Go to **Firestore Database** > **Rules**
2. Click **"Rules playground"**
3. Test different scenarios with authenticated/unauthenticated users

## Security Best Practices:

1. **Never expose API keys** in client-side code for server-side operations
2. **Use environment variables** for all sensitive configuration
3. **Implement rate limiting** for expensive operations
4. **Validate data** on both client and server side
5. **Monitor usage** to prevent abuse
6. **Use Firebase Security Rules** to protect data access
7. **Enable App Check** for production apps (optional but recommended)

## Production Deployment:

1. **Build the app**: `npm run build`
2. **Deploy to hosting** (Vercel, Netlify, Firebase Hosting)
3. **Update Firebase authorized domains** in Authentication settings
4. **Set up monitoring** and alerts
5. **Configure backup** for Firestore data