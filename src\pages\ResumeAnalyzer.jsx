import React, { useState } from 'react'
import toast from 'react-hot-toast'
import { analyzeResume, generateAtsResume, extractResumeTextFromFile } from '../services/gemini'
import {
  Upload,
  FileText,
  Sparkles,
  Loader,
  CheckCircle,
  AlertCircle,
  Download,
  Copy
} from 'lucide-react'

const ResumeAnalyzer = () => {
  const [resumeText, setResumeText] = useState('')
  const [isExtracting, setIsExtracting] = useState(false)
  const [jobDescription, setJobDescription] = useState('')
  const [analysis, setAnalysis] = useState(null)
  const [generatedResume, setGeneratedResume] = useState('')
  const [fileName, setFileName] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)

  const readAsArrayBuffer = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsArrayBuffer(file)
  })

  const readAsText = (file) => new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result)
    reader.onerror = reject
    reader.readAsText(file)
  })

  const extractTextFromPDF = async (file) => {
    try {
      const pdfjs = await import('pdfjs-dist/build/pdf')
      // Configure worker from CDN to avoid bundler worker setup
      const workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
      if (pdfjs.GlobalWorkerOptions) {
        pdfjs.GlobalWorkerOptions.workerSrc = workerSrc
      }

      const data = await readAsArrayBuffer(file)
      const loadingTask = pdfjs.getDocument({ data })
      const pdf = await loadingTask.promise
      let text = ''
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i)
        const content = await page.getTextContent()
        const strings = content.items.map((it) => it.str)
        text += strings.join(' ') + '\n'
      }
      return text
    } catch (err) {
      console.error('PDF parse failed:', err)
      throw new Error('Failed to parse PDF in browser. Install pdfjs-dist or paste resume text manually.')
    }
  }

  const handleFileChange = async (e) => {
    const file = e.target.files?.[0]
    if (!file) return

    setFileName(file.name)
    setAnalysis(null)
    setGeneratedResume('')

    try {
      setIsExtracting(true)
      let text = ''

      // Primary: Use Gemini to extract text from file (supports PDF/DOC/DOCX/TXT as bytes)
      try {
        text = await extractResumeTextFromFile(file)
      } catch (geminiErr) {
        console.warn('Gemini extraction failed, attempting local PDF parsing...', geminiErr)
        // Fallback: if PDF, try local pdf.js extraction
        if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
          text = await extractTextFromPDF(file)
        } else if (file.type.startsWith('text/') || file.name.toLowerCase().endsWith('.txt')) {
          text = await readAsText(file)
        } else {
          throw new Error('Unsupported file type. Please upload PDF or TXT.')
        }
      }

      if (!text || text.trim().length < 20) {
        throw new Error('Failed to extract meaningful text from the file.')
      }

      setResumeText(text)
      toast.success('Extracted resume text')
    } catch (error) {
      console.error(error)
      toast.error(error.message || 'Failed to read file')
    } finally {
      setIsExtracting(false)
    }
  }

  const handleAnalyze = async () => {
    if (!resumeText || resumeText.trim().length < 50) {
      toast.error('Please upload a resume first.')
      return
    }

    setIsAnalyzing(true)
    setAnalysis(null)
    try {
      const result = await analyzeResume(resumeText, jobDescription)
      setAnalysis(result)
      toast.success('ATS analysis complete')
    } catch (err) {
      console.error(err)
      toast.error(err.message || 'Failed to analyze resume')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleGenerate = async () => {
    if (!resumeText || resumeText.trim().length < 50) {
      toast.error('Please upload a resume first.')
      return
    }

    setIsGenerating(true)
    setGeneratedResume('')
    try {
      const result = await generateAtsResume(resumeText, jobDescription)
      setGeneratedResume(result?.content || result)
      toast.success('Generated ATS-optimized resume')
    } catch (err) {
      console.error(err)
      toast.error(err.message || 'Failed to generate resume')
    } finally {
      setIsGenerating(false)
    }
  }

  const copyText = async (text) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('Copied to clipboard')
    } catch {
      toast.error('Copy failed')
    }
  }

  const downloadText = (text, name = 'resume.txt') => {
    const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = name
    a.click()
    URL.revokeObjectURL(url)
  }

  const generatePdfAndDownload = async (content, filename = 'ATS_Resume.pdf') => {
    // Use the browser's print-to-PDF via a hidden iframe with CSS; or prefer a client lib.
    // Here we'll use a lightweight approach with window.print via a new tab for broad compatibility.
    // For a non-interactive auto-download, html2pdf.js could be used, but we avoid extra deps.
    const html = `<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${filename}</title>
  <style>
    body { font-family: Arial, Helvetica, sans-serif; padding: 24px; color: #111; }
    h1,h2,h3 { margin: 0 0 8px; }
    pre { white-space: pre-wrap; word-wrap: break-word; }
  </style>
</head>
<body>
  <pre>${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
  <script>
    window.onload = () => {
      window.print();
      setTimeout(() => window.close(), 500);
    };
  </script>
</body>
</html>`
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const win = window.open(url)
    // Provide a fallback manual download of the raw text as PDF not guaranteed without user print
    if (!win) {
      const txt = new Blob([content], { type: 'text/plain;charset=utf-8' })
      const txtUrl = URL.createObjectURL(txt)
      const a = document.createElement('a')
      a.href = txtUrl
      a.download = filename.replace('.pdf', '.txt')
      a.click()
      URL.revokeObjectURL(txtUrl)
    }
  }

  const ScoreBar = ({ score = 0 }) => {
    const pct = Math.max(0, Math.min(100, Math.round(score)))
    const color = pct >= 80 ? 'bg-green-500' : pct >= 60 ? 'bg-yellow-500' : 'bg-red-500'
    return (
      <div>
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">ATS Match</span>
          <span className="text-sm font-semibold text-gray-900 dark:text-gray-100">{pct}%</span>
        </div>
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div className={`h-full ${color}`} style={{ width: `${pct}%` }} />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8 px-4 dark:bg-gray-950 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-lg bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center">
              <FileText className="w-5 h-5 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Resume Analyzer</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">Analyze your resume ATS score and generate an ATS-friendly version</p>
            </div>
          </div>
          <div className="hidden md:flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${import.meta.env.VITE_GEMINI_API_KEY ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-gray-600 dark:text-gray-400">Gemini API: {import.meta.env.VITE_GEMINI_API_KEY ? 'Ready' : 'Missing'}</span>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Left: Inputs */}
          <div className="space-y-6">
            <div className="card p-6">
              <h2 className="font-semibold text-gray-900 dark:text-white mb-4">Upload or Paste Resume</h2>
              <label className="flex items-center justify-center gap-3 p-4 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 border-gray-300 dark:border-gray-700">
                <input type="file" accept=".pdf,.txt,.doc,.docx" className="hidden" onChange={handleFileChange} />
                <Upload className="w-5 h-5 text-gray-600" />
                <div className="text-sm">
                  <div className="font-medium text-gray-900 dark:text-gray-100">Choose PDF or TXT</div>
                  <div className="text-gray-600 dark:text-gray-400">{fileName || 'PDF/DOC/DOCX/TXT'}</div>
                </div>
              </label>

              <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                {isExtracting ? (
                  <div className="flex items-center gap-2"><Loader className="w-4 h-4 animate-spin" /> Extracting text from resume...</div>
                ) : (
                  <div className="text-xs">Upload your resume. Text will be extracted automatically. No need to paste.</div>
                )}
                {resumeText && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-xs text-gray-500 dark:text-gray-400">Preview extracted text</summary>
                    <pre className="mt-2 max-h-48 overflow-auto p-3 rounded bg-gray-50 dark:bg-gray-800 text-xs whitespace-pre-wrap">{resumeText}</pre>
                  </details>
                )}
              </div>
            </div>

            <div className="card p-6">
              <h2 className="font-semibold text-gray-900 dark:text-white mb-2">Target Job Description (optional)</h2>
              <textarea
                className="w-full min-h-[120px] textarea bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white mb-2"
                placeholder="Paste the job description here to tailor analysis and generation..."
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
              />
              <button
                onClick={handleAnalyze}
                disabled={isAnalyzing || !import.meta.env.VITE_GEMINI_API_KEY}
                className="mt-4 w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isAnalyzing ? (<><Loader className="w-4 h-4 animate-spin" /><span>Analyzing...</span></>) : (<><Sparkles className="w-4 h-4" /><span>Analyze ATS</span></>)}
              </button>
            </div>
          </div>

          {/* Right: Results */}
          <div className="space-y-6">
            <div className="card p-6">
              <h2 className="font-semibold text-gray-900 dark:text-white mb-4">ATS Analysis</h2>
              {!analysis ? (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Upload or paste your resume and click Analyze to see insights.
                </div>
              ) : (
                <div className="space-y-4">
                  <ScoreBar score={analysis.atsScore || analysis.score || 0} />
                  {analysis.summary && (
                    <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800 text-sm text-gray-700 dark:text-gray-300">
                      {analysis.summary}
                    </div>
                  )}

                  {Array.isArray(analysis.missingKeywords) && analysis.missingKeywords.length > 0 && (
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">Missing Keywords</div>
                      <div className="flex flex-wrap gap-2">
                        {analysis.missingKeywords.map((k, idx) => (
                          <span key={idx} className="px-2 py-1 rounded bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 text-xs">{k}</span>
                        ))}
                      </div>
                    </div>
                  )}

                  {Array.isArray(analysis.recommendations) && analysis.recommendations.length > 0 && (
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">Recommendations</div>
                      <ul className="list-disc pl-5 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                        {analysis.recommendations.map((r, idx) => (
                          <li key={idx}>{r}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {Array.isArray(analysis.formattingIssues) && analysis.formattingIssues.length > 0 && (
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-2">Formatting Issues</div>
                      <ul className="list-disc pl-5 space-y-1 text-sm text-gray-700 dark:text-gray-300">
                        {analysis.formattingIssues.map((r, idx) => (
                          <li key={idx}>{r}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="card p-6">
              <div className="flex items-center justify-between mb-3">
                <h2 className="font-semibold text-gray-900 dark:text-white">Generate ATS-friendly Resume</h2>
                <div className="text-xs text-gray-500 dark:text-gray-400">Uses Gemini to tailor and improve</div>
              </div>
              <button
                onClick={handleGenerate}
                disabled={isGenerating || !import.meta.env.VITE_GEMINI_API_KEY}
                className="mb-4 w-full btn-secondary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (<><Loader className="w-4 h-4 animate-spin" /><span>Generating...</span></>) : (<><Sparkles className="w-4 h-4" /><span>Generate Improved Resume</span></>)}
              </button>

              {!generatedResume ? (
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  After analysis, generate an ATS-optimized version tailored to your job description.
                </div>
              ) : (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <button onClick={() => copyText(generatedResume)} className="btn-tertiary flex items-center gap-2 text-sm"><Copy className="w-4 h-4" />Copy</button>
                    <button onClick={() => generatePdfAndDownload(generatedResume, 'ATS_Resume.pdf')} className="btn-tertiary flex items-center gap-2 text-sm"><Download className="w-4 h-4" />Download PDF</button>
                  </div>
                  <pre className="max-h-80 overflow-auto p-4 rounded-lg bg-gray-50 dark:bg-gray-800 text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{generatedResume}</pre>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-2 text-sm text-blue-800 dark:text-blue-200">
            <AlertCircle className="w-4 h-4 mt-0.5" />
            <div>
              <div className="font-medium">Note</div>
              <ul className="list-disc pl-5">
                <li>For best ATS compatibility, keep formatting simple: no tables, minimal icons, and clear section headings.</li>
                <li>Resume text is extracted automatically from PDF/DOC/DOCX/TXT using AI and local parsing where possible.</li>
                <li>Providing a target job description improves the accuracy of the ATS score and optimization.</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResumeAnalyzer
