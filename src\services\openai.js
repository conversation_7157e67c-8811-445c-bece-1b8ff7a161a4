import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true // Note: In production, use a backend proxy
})

// Test function to verify OpenAI connection
export const testOpenAI = async () => {
  try {
    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "write a haiku about ai"
        }
      ],
      max_tokens: 100,
      temperature: 0.7
    })
    
    console.log('OpenAI Test Response:', response.choices[0].message.content)
    return response.choices[0].message.content
  } catch (error) {
    console.error('OpenAI Test Error:', error)
    throw error
  }
}

// HR Interview Questions Generator
export const generateHRQuestions = async (jobRole, experience, difficulty = 'medium') => {
  try {
    const prompt = `Generate 5 realistic HR interview questions for a ${jobRole} position with ${experience} years of experience. 
    Difficulty level: ${difficulty}
    
    Format the response as a JSON array with objects containing:
    - question: the interview question
    - category: type of question (behavioral, situational, technical, etc.)
    - difficulty: easy, medium, or hard
    - expectedAnswer: brief guidance on what makes a good answer
    - followUp: potential follow-up question
    
    Make questions relevant to current industry standards and practices.`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert HR interviewer with 15+ years of experience. Generate realistic, relevant interview questions that assess both technical and soft skills."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error generating HR questions:', error)
    throw new Error('Failed to generate HR questions')
  }
}

// Aptitude Questions Generator
export const generateAptitudeQuestions = async (category, difficulty = 'medium', count = 10) => {
  try {
    const prompt = `Generate ${count} ${category} aptitude questions with ${difficulty} difficulty level.
    
    Categories can be: logical reasoning, quantitative aptitude, verbal ability, data interpretation
    
    Format the response as a JSON array with objects containing:
    - question: the question text
    - options: array of 4 multiple choice options (A, B, C, D)
    - correctAnswer: the correct option letter
    - explanation: detailed explanation of the solution
    - category: ${category}
    - difficulty: ${difficulty}
    - timeLimit: recommended time in seconds
    
    Ensure questions are challenging but fair, with clear explanations.`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert in creating aptitude tests for competitive exams and job interviews. Create questions that test analytical thinking and problem-solving skills."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error generating aptitude questions:', error)
    throw new Error('Failed to generate aptitude questions')
  }
}

// Code Analysis and Feedback
export const analyzeCode = async (code, language, problemDescription) => {
  try {
    const prompt = `Analyze this ${language} code solution for the following problem:
    
    Problem: ${problemDescription}
    
    Code:
    \`\`\`${language}
    ${code}
    \`\`\`
    
    Provide analysis in JSON format with:
    - timeComplexity: Big O notation
    - spaceComplexity: Big O notation
    - correctness: score out of 100
    - codeQuality: score out of 100
    - strengths: array of positive aspects
    - improvements: array of suggestions for improvement
    - bugs: array of potential bugs or issues
    - optimizations: array of optimization suggestions
    - overallScore: weighted average score
    - feedback: detailed constructive feedback
    
    Be thorough but constructive in your analysis.`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a senior software engineer and code reviewer with expertise in algorithm analysis and best practices."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.3,
      max_tokens: 2000
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error analyzing code:', error)
    throw new Error('Failed to analyze code')
  }
}

// Interview Answer Evaluation
export const evaluateAnswer = async (question, answer, context = 'hr_interview') => {
  try {
    const prompt = `Evaluate this interview answer:
    
    Question: ${question}
    Answer: ${answer}
    Context: ${context}
    
    Provide evaluation in JSON format with:
    - score: overall score out of 100
    - strengths: array of what was done well
    - improvements: array of areas for improvement
    - clarity: score for communication clarity (1-10)
    - relevance: score for answer relevance (1-10)
    - completeness: score for answer completeness (1-10)
    - confidence: detected confidence level (1-10)
    - feedback: detailed constructive feedback
    - suggestedAnswer: example of a strong answer
    - followUpQuestions: potential follow-up questions
    
    Be encouraging but honest in your evaluation.`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an experienced interview coach and HR professional. Provide constructive feedback that helps candidates improve."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.5,
      max_tokens: 1500
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error evaluating answer:', error)
    throw new Error('Failed to evaluate answer')
  }
}

// Doubt Solver
export const solveDoubt = async (question, context = '') => {
  try {
    const prompt = `Help solve this doubt/question:
    
    Question: ${question}
    ${context ? `Context: ${context}` : ''}
    
    Provide a comprehensive answer that:
    - Explains the concept clearly
    - Provides examples if applicable
    - Suggests related topics to study
    - Includes practical tips
    
    Format as JSON with:
    - answer: detailed explanation
    - examples: array of relevant examples
    - relatedTopics: array of related concepts
    - practicalTips: array of actionable advice
    - difficulty: estimated difficulty level
    - estimatedReadTime: reading time in minutes`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a knowledgeable tutor and mentor. Explain concepts clearly and provide practical guidance for interview preparation."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.6,
      max_tokens: 2000
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error solving doubt:', error)
    throw new Error('Failed to solve doubt')
  }
}

// Generate Coding Problems
export const generateCodingProblem = async (difficulty, topic = 'general') => {
  try {
    const prompt = `Generate a coding problem with ${difficulty} difficulty level on topic: ${topic}
    
    Format as JSON with:
    - title: problem title
    - description: detailed problem description
    - examples: array of input/output examples
    - constraints: array of constraints
    - hints: array of helpful hints
    - starterCode: object with javascript and python starter code
    - solution: object with javascript and python solutions
    - testCases: array of test cases with input/expected output
    - timeComplexity: expected time complexity
    - spaceComplexity: expected space complexity
    - difficulty: ${difficulty}
    - topic: ${topic}
    - companies: array of companies that have asked similar questions`

    const response = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert in creating coding interview problems. Generate realistic problems similar to those asked by top tech companies."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000
    })

    const content = response.choices[0].message.content
    return JSON.parse(content)
  } catch (error) {
    console.error('Error generating coding problem:', error)
    throw new Error('Failed to generate coding problem')
  }
}