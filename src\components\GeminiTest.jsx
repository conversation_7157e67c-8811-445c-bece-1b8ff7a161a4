import React, { useState } from 'react'
import { testG<PERSON>ini, generateCodingProblem, generateAptitudeQuestions, evaluateAnswer } from '../services/gemini'
import toast from 'react-hot-toast'
import { 
  Play, 
  CheckCircle, 
  XCircle, 
  Loader, 
  Code, 
  Brain, 
  MessageSquare,
  Sparkles
} from 'lucide-react'

const GeminiTest = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState({})

  const runTest = async (testName, testFunction) => {
    setIsLoading(true)
    try {
      const result = await testFunction()
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, data: result }
      }))
      toast.success(`${testName} test passed!`)
    } catch (error) {
      console.error(`${testName} test failed:`, error)
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, error: error.message }
      }))
      toast.error(`${testName} test failed: ${error.message}`)
    } finally {
      setIsLoading(false)
    }
  }

  const tests = [
    {
      name: 'Basic Connection',
      description: 'Test basic Gemini API connection',
      icon: Sparkles,
      color: 'blue',
      testFunction: testGemini
    },
    {
      name: 'Coding Problem Generation',
      description: 'Generate a coding problem using AI',
      icon: Code,
      color: 'green',
      testFunction: () => generateCodingProblem('medium', 'arrays')
    },
    {
      name: 'Aptitude Questions',
      description: 'Generate aptitude questions',
      icon: Brain,
      color: 'purple',
      testFunction: () => generateAptitudeQuestions('logical', 'medium', 3)
    },
    {
      name: 'Answer Evaluation',
      description: 'Evaluate an interview answer',
      icon: MessageSquare,
      color: 'orange',
      testFunction: () => evaluateAnswer(
        'Tell me about yourself',
        'I am a software engineer with 3 years of experience in web development. I have worked with React, Node.js, and databases.',
        'hr_interview'
      )
    }
  ]

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 border-blue-200 dark:border-blue-800',
      green: 'bg-green-100 dark:bg-green-900/30 text-green-600 border-green-200 dark:border-green-800',
      purple: 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 border-purple-200 dark:border-purple-800',
      orange: 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 border-orange-200 dark:border-orange-800'
    }
    return colors[color] || colors.blue
  }

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Gemini AI Integration Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Test all Gemini AI features to ensure proper integration
          </p>
        </div>

        {/* API Key Status */}
        <div className="card p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Configuration Status
          </h2>
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              import.meta.env.VITE_GEMINI_API_KEY ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <span className="text-gray-700 dark:text-gray-300">
              Gemini API Key: {import.meta.env.VITE_GEMINI_API_KEY ? 'Configured' : 'Missing'}
            </span>
          </div>
          {!import.meta.env.VITE_GEMINI_API_KEY && (
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                <strong>Setup Required:</strong> Please add your Gemini API key to the .env file as VITE_GEMINI_API_KEY
              </p>
            </div>
          )}
        </div>

        {/* Test Grid */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          {tests.map((test) => {
            const result = testResults[test.name]
            const IconComponent = test.icon
            
            return (
              <div key={test.name} className="card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-lg border flex items-center justify-center ${getColorClasses(test.color)}`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {test.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {test.description}
                      </p>
                    </div>
                  </div>
                  
                  {result && (
                    <div className="flex-shrink-0">
                      {result.success ? (
                        <CheckCircle className="w-6 h-6 text-green-500" />
                      ) : (
                        <XCircle className="w-6 h-6 text-red-500" />
                      )}
                    </div>
                  )}
                </div>

                <button
                  onClick={() => runTest(test.name, test.testFunction)}
                  disabled={isLoading || !import.meta.env.VITE_GEMINI_API_KEY}
                  className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {isLoading ? (
                    <>
                      <Loader className="w-4 h-4 animate-spin" />
                      <span>Testing...</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span>Run Test</span>
                    </>
                  )}
                </button>

                {result && (
                  <div className="mt-4">
                    {result.success ? (
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                        <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">
                          ✅ Test Passed
                        </h4>
                        <div className="text-sm text-green-700 dark:text-green-300 max-h-32 overflow-y-auto">
                          <pre className="whitespace-pre-wrap">
                            {typeof result.data === 'string' 
                              ? result.data 
                              : JSON.stringify(result.data, null, 2)
                            }
                          </pre>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                        <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
                          ❌ Test Failed
                        </h4>
                        <p className="text-sm text-red-700 dark:text-red-300">
                          {result.error}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Overall Status */}
        <div className="card p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Overall Status
          </h2>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {Object.keys(testResults).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tests Run
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {Object.values(testResults).filter(r => r.success).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tests Passed
              </div>
            </div>
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600 mb-1">
                {Object.values(testResults).filter(r => !r.success).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Tests Failed
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default GeminiTest