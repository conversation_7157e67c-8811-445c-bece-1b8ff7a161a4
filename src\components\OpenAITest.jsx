import React, { useState } from 'react'
import { testOpenAI, generateHRQuestions, generateAptitudeQuestions, evaluateAnswer } from '../services/openai'
import { Loader, CheckCircle, XCircle, Play } from 'lucide-react'

const OpenAITest = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)
  const [testType, setTestType] = useState('haiku')

  const runTest = async (type) => {
    setIsLoading(true)
    setError(null)
    setResult(null)
    setTestType(type)

    try {
      let response
      
      switch (type) {
        case 'haiku':
          response = await testOpenAI()
          break
          
        case 'hr-questions':
          response = await generateHRQuestions('Software Engineer', '3', 'medium')
          break
          
        case 'aptitude':
          response = await generateAptitudeQuestions('logical reasoning', 'medium', 3)
          break
          
        case 'evaluate':
          response = await evaluate<PERSON><PERSON>wer(
            'Tell me about yourself',
            'I am a software engineer with 3 years of experience in React and Node.js. I enjoy solving complex problems and building user-friendly applications.',
            'hr_interview'
          )
          break
          
        default:
          throw new Error('Unknown test type')
      }
      
      setResult(response)
    } catch (err) {
      setError(err.message)
      console.error('Test error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="card p-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
          OpenAI Integration Test
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <button
            onClick={() => runTest('haiku')}
            disabled={isLoading}
            className="btn-primary disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>Test Haiku</span>
          </button>
          
          <button
            onClick={() => runTest('hr-questions')}
            disabled={isLoading}
            className="btn-secondary disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>HR Questions</span>
          </button>
          
          <button
            onClick={() => runTest('aptitude')}
            disabled={isLoading}
            className="btn-secondary disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>Aptitude</span>
          </button>
          
          <button
            onClick={() => runTest('evaluate')}
            disabled={isLoading}
            className="btn-secondary disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            <Play className="w-4 h-4" />
            <span>Evaluate</span>
          </button>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader className="w-8 h-8 animate-spin text-primary-600 mr-3" />
            <span className="text-gray-600 dark:text-gray-400">
              Testing OpenAI {testType}...
            </span>
          </div>
        )}

        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
            <div className="flex items-center space-x-2">
              <XCircle className="w-5 h-5 text-red-600" />
              <span className="font-medium text-red-800 dark:text-red-200">Error:</span>
            </div>
            <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
          </div>
        )}

        {result && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-800 dark:text-green-200">
                Success! OpenAI Response:
              </span>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border">
              <pre className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap overflow-auto">
                {typeof result === 'string' ? result : JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>API Key Status:</strong> {import.meta.env.VITE_OPENAI_API_KEY ? '✅ Configured' : '❌ Missing'}</p>
          <p><strong>Environment:</strong> {import.meta.env.MODE}</p>
        </div>
      </div>
    </div>
  )
}

export default OpenAITest