import { GoogleGenerativeAI } from '@google/generative-ai'

// Helpers: retry, model fallback, and centralized request execution
const sleep = (ms) => new Promise((r) => setTimeout(r, ms))

const isTransient = (error) => {
  const s = `${error?.status || ''} ${error?.message || error}`
  return /(503|429|overloaded|unavailable|timeout|timed out|quota|exceeded|Resource has been exhausted)/i.test(s)
}

const withRetry = async (fn, { retries = 2, baseDelay = 600 } = {}) => {
  let attempt = 0
  while (true) {
    try {
      return await fn()
    } catch (err) {
      attempt += 1
      if (attempt > retries || !isTransient(err)) throw err
      const jitter = Math.random() * baseDelay
      const delay = baseDelay * Math.pow(2, attempt - 1) + jitter
      // eslint-disable-next-line no-console
      console.warn(`Gemini transient error. Retry ${attempt}/${retries} in ${Math.round(delay)}ms`, err?.message || err)
      await sleep(delay)
    }
  }
}

const getModels = () => {
  const apiKey = import.meta.env.VITE_GEMINI_API_KEY
  if (!apiKey) throw new Error('Missing Gemini API key (VITE_GEMINI_API_KEY)')
  const genAI = new GoogleGenerativeAI(apiKey)
  return [
    genAI.getGenerativeModel({ model: 'gemini-1.5-flash' }),
    genAI.getGenerativeModel({ model: 'gemini-1.5-flash-8b' }),
    genAI.getGenerativeModel({ model: 'gemini-1.0-pro' })
  ]
}

const generateText = async (promptOrParts) => {
  const models = getModels()
  let lastError
  for (const model of models) {
    try {
      const result = await withRetry(() => model.generateContent(promptOrParts), { retries: 2, baseDelay: 700 })
      const response = await result.response
      const text = response.text()
      if (!text || !text.trim()) throw new Error('Empty response from Gemini')
      return text
    } catch (err) {
      lastError = err
      // If not transient, break immediately
      if (!isTransient(err)) break
      // else try next model
    }
  }
  throw lastError || new Error('Gemini request failed')
}

// JSON parsing with resilience
const parseGeminiJSON = (text) => {
  try {
    // Remove markdown code fences
    let cleanText = text.replace(/```json\n?/gi, '').replace(/```\n?/gi, '').trim()
    // Remove trailing commas before closing braces/brackets
    cleanText = cleanText.replace(/,(\s*[}\]])/g, '$1')
    return JSON.parse(cleanText)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to parse JSON from Gemini response:', text)
    throw new Error('Invalid JSON response from Gemini')
  }
}

// Public API
export const testGemini = async () => {
  const prompt = 'Write a haiku about AI'
  try {
    const text = await generateText(prompt)
    // eslint-disable-next-line no-console
    console.log('Gemini Test Response:', text)
    return text
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Gemini Test Error:', error)
    throw error
  }
}

// HR Interview Questions Generator
export const generateHRQuestions = async (jobRole, experience, difficulty = 'medium') => {
  try {
    const prompt = `You are an expert HR interviewer with 15+ years of experience. Generate realistic, relevant interview questions that assess both technical and soft skills.

Generate 5 realistic HR interview questions for a ${jobRole} position with ${experience} years of experience. 
Difficulty level: ${difficulty}

Format the response as a JSON array with objects containing:
- question: the interview question
- category: type of question (behavioral, situational, technical, etc.)
- difficulty: easy, medium, or hard
- expectedAnswer: brief guidance on what makes a good answer
- followUp: potential follow-up question

Make questions relevant to current industry standards and practices.

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error generating HR questions:', error)
    throw new Error('Failed to generate HR questions')
  }
}

// Aptitude Questions Generator
export const generateAptitudeQuestions = async (category, difficulty = 'medium', count = 10) => {
  try {
    const prompt = `You are an expert in creating aptitude tests for competitive exams and job interviews. Create questions that test analytical thinking and problem-solving skills.

Generate ${count} ${category} aptitude questions with ${difficulty} difficulty level.

Categories can be: logical reasoning, quantitative aptitude, verbal ability, data interpretation

Format the response as a JSON array with objects containing:
- question: the question text
- options: array of 4 multiple choice options
- correctAnswer: the correct option index (0, 1, 2, or 3)
- explanation: detailed explanation of the solution
- category: ${category}
- difficulty: ${difficulty}
- timeLimit: recommended time in seconds

Ensure questions are challenging but fair, with clear explanations.

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error generating aptitude questions:', error)
    throw new Error('Failed to generate aptitude questions')
  }
}

// Code Analysis and Feedback
export const analyzeCode = async (code, language, problemDescription) => {
  try {
    const prompt = `You are a senior software engineer and code reviewer with expertise in algorithm analysis and best practices.

Analyze this ${language} code solution for the following problem:

Problem: ${problemDescription}

Code:
\`\`\`${language}
${code}
\`\`\`

Provide analysis in JSON format with:
- timeComplexity: Big O notation
- spaceComplexity: Big O notation
- correctness: score out of 100
- codeQuality: score out of 100
- strengths: array of positive aspects
- improvements: array of suggestions for improvement
- bugs: array of potential bugs or issues
- optimizations: array of optimization suggestions
- overallScore: weighted average score
- feedback: detailed constructive feedback

Be thorough but constructive in your analysis.

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error analyzing code:', error)
    throw new Error('Failed to analyze code')
  }
}

// Interview Answer Evaluation
export const evaluateAnswer = async (question, answer, context = 'hr_interview') => {
  try {
    const prompt = `You are an experienced interview coach and HR professional. Provide constructive feedback that helps candidates improve.

Evaluate this interview answer:

Question: ${question}
Answer: ${answer}
Context: ${context}

Provide evaluation in JSON format with:
- score: overall score out of 100
- strengths: array of what was done well
- improvements: array of areas for improvement
- clarity: score for communication clarity (1-10)
- relevance: score for answer relevance (1-10)
- completeness: score for answer completeness (1-10)
- confidence: detected confidence level (1-10)
- feedback: detailed constructive feedback
- suggestedAnswer: example of a strong answer
- followUpQuestions: potential follow-up questions

Be encouraging but honest in your evaluation.

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error evaluating answer:', error)
    throw new Error('Failed to evaluate answer')
  }
}

// Doubt Solver
export const solveDoubt = async (question, context = '') => {
  try {
    const prompt = `You are a knowledgeable tutor and mentor. Explain concepts clearly and provide practical guidance for interview preparation.

Help solve this doubt/question:

Question: ${question}
${context ? `Context: ${context}` : ''}

Provide a comprehensive answer that:
- Explains the concept clearly
- Provides examples if applicable
- Suggests related topics to study
- Includes practical tips

Format as JSON with:
- answer: detailed explanation
- examples: array of relevant examples (as strings, not objects)
- relatedTopics: array of related concepts (as strings)
- practicalTips: array of actionable advice (as strings)
- difficulty: estimated difficulty level
- estimatedReadTime: reading time in minutes

Important: Make sure examples are simple strings that clearly explain the concept with concrete examples. Do not use nested objects for examples.

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error solving doubt:', error)
    throw new Error('Failed to solve doubt')
  }
}

// Resume Analysis
export const analyzeResume = async (resumeText, jobDescription = '') => {
  try {
    const prompt = `You are an ATS (Applicant Tracking System) expert and resume reviewer.

Analyze the following resume text${jobDescription ? ' in the context of this job description' : ''} and return a JSON object ONLY (no markdown) with:
- atsScore: number 0-100 estimating ATS match percentage
- summary: 2-3 sentence summary of overall fit
- missingKeywords: array of important keywords missing that are relevant to the role
- recommendations: array of actionable suggestions to improve the resume for ATS (concise, bullet-friendly)
- formattingIssues: array of potential formatting issues that hurt ATS parsing (e.g., tables, icons, columns, images)
- sectionScores: object with keys like skills, experience, education, projects and numeric scores

Resume Text:
${resumeText}

${jobDescription ? `Job Description:\n${jobDescription}` : ''}

Return valid JSON only.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error analyzing resume:', error)
    throw new Error('Failed to analyze resume')
  }
}

export const extractResumeTextFromFile = async (file) => {
  try {
    const toBase64 = (f) => new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result
        if (typeof result === 'string' && result.startsWith('data:')) {
          const base64 = result.split(',')[1]
          resolve(base64)
        } else if (result instanceof ArrayBuffer) {
          const bytes = new Uint8Array(result)
          let binary = ''
          for (let i = 0; i < bytes.byteLength; i++) binary += String.fromCharCode(bytes[i])
          resolve(btoa(binary))
        } else {
          resolve('')
        }
      }
      reader.onerror = reject
      reader.readAsDataURL(f)
    })

    const base64 = await toBase64(file)

    const prompt = {
      text: 'Extract the raw plain text content from this resume file. Return ONLY the plain text with basic line breaks. Do not include any markdown, bullets, headers, or commentary.'
    }

    const filePart = {
      inlineData: {
        mimeType: file.type || 'application/pdf',
        data: base64
      }
    }

    const text = await generateText([prompt, filePart])
    return text.trim()
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error extracting resume text via Gemini:', error)
    throw new Error('Failed to extract resume text')
  }
}

export const generateAtsResume = async (resumeText, jobDescription = '') => {
  try {
    const prompt = `You are a professional resume writer specializing in ATS-optimized resumes.

Rewrite and improve the following resume into an ATS-friendly plain text format, tailored${jobDescription ? ' to this job description' : ''}. Keep simple formatting with clear section headers and bullet points. Avoid tables, icons, images, and complex layouts. Use strong action verbs and measurable impact where possible.

Sections to include (if content exists):
- Header (Name | Email | Phone | LinkedIn/GitHub)
- Summary
- Skills (grouped by category)
- Experience (reverse-chronological, bullets)
- Projects (impact-focused bullets)
- Education
- Certifications/Awards (optional)

Resume Text:
${resumeText}

${jobDescription ? `Target Job Description:\n${jobDescription}` : ''}

Return ONLY the improved resume as plain text with headings and bullets.`

    const content = await generateText(prompt)
    return content
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error generating ATS resume:', error)
    throw new Error('Failed to generate ATS resume')
  }
}

export const generateCodingProblem = async (difficulty, topic = 'general') => {
  try {
    const prompt = `You are an expert in creating coding interview problems. Generate realistic problems similar to those asked by top tech companies.

Generate a coding problem with ${difficulty} difficulty level on topic: ${topic}

Format as JSON with:
- title: problem title
- description: detailed problem description
- examples: array of input/output examples with input and output fields
- constraints: array of constraints
- hints: array of helpful hints
- starterCode: object with javascript and python starter code
- solution: object with javascript and python solutions
- testCases: array of test cases with input and expected fields
- timeComplexity: expected time complexity
- spaceComplexity: expected space complexity
- difficulty: ${difficulty}
- topic: ${topic}
- companies: array of companies that have asked similar questions

Return only valid JSON without any markdown formatting.`

    const content = await generateText(prompt)
    return parseGeminiJSON(content)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error generating coding problem:', error)
    throw new Error('Failed to generate coding problem')
  }
}

// Chat with Gemini (general purpose)
export const chatWithGemini = async (message, context = '') => {
  try {
    const prompt = context ? `${context}\n\nUser: ${message}` : message
    const text = await generateText(prompt)
    return text
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Error chatting with Gemini:', error)
    throw new Error('Failed to get response from Gemini')
  }
}

export default {
  testGemini,
  generateHRQuestions,
  generateAptitudeQuestions,
  analyzeCode,
  evaluateAnswer,
  solveDoubt,
  generateCodingProblem,
  chatWithGemini
}
