import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY)

// Get the Gemini Flash model (updated model name)
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" })

// Test function to verify Gemini connection
export const testGemini = async () => {
  try {
    const prompt = "Write a haiku about AI"
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()
    
    console.log('Gemini Test Response:', text)
    return text
  } catch (error) {
    console.error('Gemini Test Error:', error)
    throw error
  }
}

// Helper function to parse JSON from Gemini response
const parseGeminiJSON = (text) => {
  try {
    // Remove markdown code blocks if present
    let cleanText = text.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()

    // Fix common JSON issues
    // Remove trailing commas before closing brackets/braces
    cleanText = cleanText.replace(/,(\s*[}\]])/g, '$1')

    const parsed = JSON.parse(cleanText)

    // Debug logging to help identify object vs string issues
    if (parsed.examples && Array.isArray(parsed.examples)) {
      console.log('Examples structure:', parsed.examples.map((ex, i) => ({
        index: i,
        type: typeof ex,
        isObject: typeof ex === 'object' && ex !== null,
        keys: typeof ex === 'object' && ex !== null ? Object.keys(ex) : null,
        value: ex
      })))
    }

    return parsed
  } catch (error) {
    console.error('Failed to parse JSON from Gemini response:', text)
    throw new Error('Invalid JSON response from Gemini')
  }
}

// HR Interview Questions Generator
export const generateHRQuestions = async (jobRole, experience, difficulty = 'medium') => {
  try {
    const prompt = `You are an expert HR interviewer with 15+ years of experience. Generate realistic, relevant interview questions that assess both technical and soft skills.

Generate 5 realistic HR interview questions for a ${jobRole} position with ${experience} years of experience. 
Difficulty level: ${difficulty}

Format the response as a JSON array with objects containing:
- question: the interview question
- category: type of question (behavioral, situational, technical, etc.)
- difficulty: easy, medium, or hard
- expectedAnswer: brief guidance on what makes a good answer
- followUp: potential follow-up question

Make questions relevant to current industry standards and practices.

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error generating HR questions:', error)
    throw new Error('Failed to generate HR questions')
  }
}

// Aptitude Questions Generator
export const generateAptitudeQuestions = async (category, difficulty = 'medium', count = 10) => {
  try {
    const prompt = `You are an expert in creating aptitude tests for competitive exams and job interviews. Create questions that test analytical thinking and problem-solving skills.

Generate ${count} ${category} aptitude questions with ${difficulty} difficulty level.

Categories can be: logical reasoning, quantitative aptitude, verbal ability, data interpretation

Format the response as a JSON array with objects containing:
- question: the question text
- options: array of 4 multiple choice options
- correctAnswer: the correct option index (0, 1, 2, or 3)
- explanation: detailed explanation of the solution
- category: ${category}
- difficulty: ${difficulty}
- timeLimit: recommended time in seconds

Ensure questions are challenging but fair, with clear explanations.

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error generating aptitude questions:', error)
    throw new Error('Failed to generate aptitude questions')
  }
}

// Code Analysis and Feedback
export const analyzeCode = async (code, language, problemDescription) => {
  try {
    const prompt = `You are a senior software engineer and code reviewer with expertise in algorithm analysis and best practices.

Analyze this ${language} code solution for the following problem:

Problem: ${problemDescription}

Code:
\`\`\`${language}
${code}
\`\`\`

Provide analysis in JSON format with:
- timeComplexity: Big O notation
- spaceComplexity: Big O notation
- correctness: score out of 100
- codeQuality: score out of 100
- strengths: array of positive aspects
- improvements: array of suggestions for improvement
- bugs: array of potential bugs or issues
- optimizations: array of optimization suggestions
- overallScore: weighted average score
- feedback: detailed constructive feedback

Be thorough but constructive in your analysis.

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error analyzing code:', error)
    throw new Error('Failed to analyze code')
  }
}

// Interview Answer Evaluation
export const evaluateAnswer = async (question, answer, context = 'hr_interview') => {
  try {
    const prompt = `You are an experienced interview coach and HR professional. Provide constructive feedback that helps candidates improve.

Evaluate this interview answer:

Question: ${question}
Answer: ${answer}
Context: ${context}

Provide evaluation in JSON format with:
- score: overall score out of 100
- strengths: array of what was done well
- improvements: array of areas for improvement
- clarity: score for communication clarity (1-10)
- relevance: score for answer relevance (1-10)
- completeness: score for answer completeness (1-10)
- confidence: detected confidence level (1-10)
- feedback: detailed constructive feedback
- suggestedAnswer: example of a strong answer
- followUpQuestions: potential follow-up questions

Be encouraging but honest in your evaluation.

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error evaluating answer:', error)
    throw new Error('Failed to evaluate answer')
  }
}

// Doubt Solver
export const solveDoubt = async (question, context = '') => {
  try {
    const prompt = `You are a knowledgeable tutor and mentor. Explain concepts clearly and provide practical guidance for interview preparation.

Help solve this doubt/question:

Question: ${question}
${context ? `Context: ${context}` : ''}

Provide a comprehensive answer that:
- Explains the concept clearly
- Provides examples if applicable
- Suggests related topics to study
- Includes practical tips

Format as JSON with:
- answer: detailed explanation
- examples: array of relevant examples (as strings, not objects)
- relatedTopics: array of related concepts (as strings)
- practicalTips: array of actionable advice (as strings)
- difficulty: estimated difficulty level
- estimatedReadTime: reading time in minutes

Important: Make sure examples are simple strings that clearly explain the concept with concrete examples. Do not use nested objects for examples.

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error solving doubt:', error)
    throw new Error('Failed to solve doubt')
  }
}

// Generate Coding Problems
export const generateCodingProblem = async (difficulty, topic = 'general') => {
  try {
    const prompt = `You are an expert in creating coding interview problems. Generate realistic problems similar to those asked by top tech companies.

Generate a coding problem with ${difficulty} difficulty level on topic: ${topic}

Format as JSON with:
- title: problem title
- description: detailed problem description
- examples: array of input/output examples with input and output fields
- constraints: array of constraints
- hints: array of helpful hints
- starterCode: object with javascript and python starter code
- solution: object with javascript and python solutions
- testCases: array of test cases with input and expected fields
- timeComplexity: expected time complexity
- spaceComplexity: expected space complexity
- difficulty: ${difficulty}
- topic: ${topic}
- companies: array of companies that have asked similar questions

Return only valid JSON without any markdown formatting.`

    const result = await model.generateContent(prompt)
    const response = await result.response
    const content = response.text()
    return parseGeminiJSON(content)
  } catch (error) {
    console.error('Error generating coding problem:', error)
    throw new Error('Failed to generate coding problem')
  }
}

// Chat with Gemini (general purpose)
export const chatWithGemini = async (message, context = '') => {
  try {
    const prompt = context ? `${context}\n\nUser: ${message}` : message
    
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()
    
    return text
  } catch (error) {
    console.error('Error chatting with Gemini:', error)
    throw new Error('Failed to get response from Gemini')
  }
}

export default {
  testGemini,
  generateHRQuestions,
  generateAptitudeQuestions,
  analyzeCode,
  evaluateAnswer,
  solveDoubt,
  generateCodingProblem,
  chatWithGemini
}