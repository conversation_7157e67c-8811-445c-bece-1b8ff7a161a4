import React, { useState, useEffect } from 'react'
import { useUser } from '../contexts/UserContext'
import CodeMirror from '@uiw/react-codemirror'
import { javascript } from '@codemirror/lang-javascript'
import { python } from '@codemirror/lang-python'
import { cpp } from '@codemirror/lang-cpp'
import { oneDark } from '@codemirror/theme-one-dark'
import { useTheme } from '../contexts/ThemeContext'
import { generateCodingProblem, analyzeCode } from '../services/gemini'
import { CodeExecutionService } from '../services/codeExecution'
import toast from 'react-hot-toast'
import { 
  Code, 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Trophy, 
  Star,
  Lightbulb,
  RotateCcw,
  Eye,
  EyeOff,
  Zap,
  Target,
  TrendingUp,
  Award,
  Shuffle,
  BookOpen,
  Brain,
  Database,
  GitBranch,
  Hash,
  Layers,
  Network,
  Search,
  BarChart3,
  Cpu,
  Loader,
  ArrowLeft,
  Timer,
  <PERSON>ug,
  <PERSON><PERSON><PERSON>,
  MessageCircle,
  Users
} from 'lucide-react'

const Coding<PERSON>hallenge = () => {
  const { user, updateUserStats } = useUser()
  const { isDark } = useTheme()
  const [selectedTopic, setSelectedTopic] = useState(null)
  const [selectedProblem, setSelectedProblem] = useState(null)
  const [code, setCode] = useState('')
  const [language, setLanguage] = useState('javascript')
  const [output, setOutput] = useState('')
  const [isRunning, setIsRunning] = useState(false)
  const [testResults, setTestResults] = useState([])
  const [showSolution, setShowSolution] = useState(false)
  const [aiAnalysis, setAiAnalysis] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [completedProblems, setCompletedProblems] = useState([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [userProgress, setUserProgress] = useState({})
  const [timeSpent, setTimeSpent] = useState(0)
  const [startTime, setStartTime] = useState(null)
  
  // Lock state - set to false to unlock the coding section
  const [isLocked, setIsLocked] = useState(false)

  // Comprehensive DSA Topics
  const dsaTopics = [
    {
      id: 'arrays',
      name: 'Arrays & Strings',
      description: 'Master array manipulation, string processing, and two-pointer techniques',
      icon: Database,
      color: 'from-blue-500 to-cyan-500',
      difficulty: 'Easy to Medium',
      totalProblems: 25,
      concepts: ['Two Pointers', 'Sliding Window', 'Prefix Sum', 'Kadane\'s Algorithm']
    },
    {
      id: 'linked-lists',
      name: 'Linked Lists',
      description: 'Learn pointer manipulation, reversal, and cycle detection',
      icon: GitBranch,
      color: 'from-green-500 to-emerald-500',
      difficulty: 'Easy to Medium',
      totalProblems: 20,
      concepts: ['Reversal', 'Cycle Detection', 'Merge Operations', 'Fast & Slow Pointers']
    },
    {
      id: 'stacks-queues',
      name: 'Stacks & Queues',
      description: 'Understand LIFO/FIFO operations and monotonic structures',
      icon: Layers,
      color: 'from-purple-500 to-pink-500',
      difficulty: 'Easy to Medium',
      totalProblems: 18,
      concepts: ['Monotonic Stack', 'Queue using Stacks', 'Expression Evaluation', 'Next Greater Element']
    },
    {
      id: 'trees',
      name: 'Binary Trees',
      description: 'Tree traversals, BST operations, and tree construction',
      icon: Network,
      color: 'from-orange-500 to-red-500',
      difficulty: 'Medium',
      totalProblems: 30,
      concepts: ['DFS/BFS', 'BST Operations', 'Tree Construction', 'LCA Problems']
    },
    {
      id: 'graphs',
      name: 'Graphs',
      description: 'Graph traversal, shortest paths, and topological sorting',
      icon: Network,
      color: 'from-indigo-500 to-purple-500',
      difficulty: 'Medium to Hard',
      totalProblems: 25,
      concepts: ['DFS/BFS', 'Dijkstra', 'Union Find', 'Topological Sort']
    },
    {
      id: 'dynamic-programming',
      name: 'Dynamic Programming',
      description: 'Optimization problems using memoization and tabulation',
      icon: Brain,
      color: 'from-red-500 to-pink-500',
      difficulty: 'Medium to Hard',
      totalProblems: 35,
      concepts: ['1D/2D DP', 'Knapsack', 'LIS/LCS', 'State Machines']
    },
    {
      id: 'backtracking',
      name: 'Backtracking',
      description: 'Generate all possible solutions using recursive exploration',
      icon: Search,
      color: 'from-yellow-500 to-orange-500',
      difficulty: 'Medium',
      totalProblems: 20,
      concepts: ['Permutations', 'Combinations', 'N-Queens', 'Sudoku Solver']
    },
    {
      id: 'greedy',
      name: 'Greedy Algorithms',
      description: 'Make locally optimal choices for global optimization',
      icon: Target,
      color: 'from-green-500 to-blue-500',
      difficulty: 'Medium',
      totalProblems: 22,
      concepts: ['Interval Scheduling', 'Huffman Coding', 'Activity Selection', 'Fractional Knapsack']
    },
    {
      id: 'sorting-searching',
      name: 'Sorting & Searching',
      description: 'Binary search variations and custom sorting algorithms',
      icon: BarChart3,
      color: 'from-teal-500 to-green-500',
      difficulty: 'Easy to Medium',
      totalProblems: 18,
      concepts: ['Binary Search', 'Quick Sort', 'Merge Sort', 'Custom Comparators']
    },
    {
      id: 'hash-tables',
      name: 'Hash Tables',
      description: 'Efficient lookups, frequency counting, and collision handling',
      icon: Hash,
      color: 'from-pink-500 to-red-500',
      difficulty: 'Easy to Medium',
      totalProblems: 15,
      concepts: ['Frequency Maps', 'Two Sum Variants', 'Anagram Detection', 'LRU Cache']
    },
    {
      id: 'heaps',
      name: 'Heaps & Priority Queues',
      description: 'Min/max heaps for efficient priority-based operations',
      icon: TrendingUp,
      color: 'from-violet-500 to-purple-500',
      difficulty: 'Medium',
      totalProblems: 16,
      concepts: ['Top K Elements', 'Merge K Lists', 'Median Finding', 'Task Scheduler']
    },
    {
      id: 'bit-manipulation',
      name: 'Bit Manipulation',
      description: 'Bitwise operations for efficient problem solving',
      icon: Cpu,
      color: 'from-gray-500 to-slate-500',
      difficulty: 'Medium',
      totalProblems: 12,
      concepts: ['XOR Properties', 'Bit Masks', 'Power of Two', 'Single Number']
    }
  ]

  useEffect(() => {
    if (selectedProblem && !startTime) {
      setStartTime(Date.now())
    }
  }, [selectedProblem])

  useEffect(() => {
    let interval = null
    if (startTime && selectedProblem) {
      interval = setInterval(() => {
        setTimeSpent(Math.floor((Date.now() - startTime) / 1000))
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [startTime, selectedProblem])

  const generateRandomProblem = async (topic) => {
    setIsGenerating(true)
    try {
      const difficulties = ['easy', 'medium', 'hard']
      const randomDifficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
      
      const problem = await generateCodingProblem(randomDifficulty, topic.id)
      
      // Add unique ID and metadata
      const enhancedProblem = {
        ...problem,
        id: Date.now(),
        topic: topic.id,
        generatedAt: new Date().toISOString(),
        timeLimit: randomDifficulty === 'easy' ? 20 : randomDifficulty === 'medium' ? 35 : 50
      }
      
      setSelectedProblem(enhancedProblem)
      setCode(enhancedProblem.starterCode[language] || getDefaultStarterCode(language))
      toast.success('New problem generated!')
    } catch (error) {
      console.error('Error generating problem:', error)
      toast.error('Failed to generate problem. Using fallback.')
      
      // Fallback to a sample problem based on topic
      const fallbackProblem = getFallbackProblem(topic, randomDifficulty)
      
      setSelectedProblem(fallbackProblem)
      setCode(fallbackProblem.starterCode[language])
    } finally {
      setIsGenerating(false)
    }
  }

  const getFallbackProblem = (topic, difficulty) => {
    const problems = {
      'linked-lists': {
        title: 'Reverse Linked List',
        description: 'Given the head of a singly linked list, reverse the list, and return the reversed list.',
        examples: [
          { input: '[1,2,3,4,5]', output: '[5,4,3,2,1]' },
          { input: '[1,2]', output: '[2,1]' },
          { input: '[]', output: '[]' }
        ],
        starterCode: {
          javascript: `/**\n * Definition for singly-linked list.\n * function ListNode(val, next) {\n *     this.val = (val===undefined ? 0 : val)\n *     this.next = (next===undefined ? null : next)\n * }\n */\nfunction reverseList(head) {\n    // Your code here\n    \n}`,
          python: `# Definition for singly-linked list.\nclass ListNode:\n    def __init__(self, val=0, next=None):\n        self.val = val\n        self.next = next\n\ndef reverseList(head):\n    # Your code here\n    pass`,
          cpp: `/**\n * Definition for singly-linked list.\n * struct ListNode {\n *     int val;\n *     ListNode *next;\n *     ListNode() : val(0), next(nullptr) {}\n *     ListNode(int x) : val(x), next(nullptr) {}\n *     ListNode(int x, ListNode *next) : val(x), next(next) {}\n * };\n */\nclass Solution {\npublic:\n    ListNode* reverseList(ListNode* head) {\n        // Your code here\n        \n    }\n};`
        },
        testCases: [
          { input: '[1,2,3,4,5]', expected: '[5,4,3,2,1]' },
          { input: '[1,2]', expected: '[2,1]' },
          { input: '[]', expected: '[]' }
        ]
      },
      'arrays': {
        title: 'Two Sum',
        description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.',
        examples: [
          { input: 'nums = [2,7,11,15], target = 9', output: '[0,1]' },
          { input: 'nums = [3,2,4], target = 6', output: '[1,2]' },
          { input: 'nums = [3,3], target = 6', output: '[0,1]' }
        ],
        starterCode: {
          javascript: `function twoSum(nums, target) {\n    // Your code here\n    \n}`,
          python: `def twoSum(nums, target):\n    # Your code here\n    pass`,
          cpp: `class Solution {\npublic:\n    vector<int> twoSum(vector<int>& nums, int target) {\n        // Your code here\n        \n    }\n};`
        },
        testCases: [
          { input: '[2,7,11,15], 9', expected: '[0,1]' },
          { input: '[3,2,4], 6', expected: '[1,2]' },
          { input: '[3,3], 6', expected: '[0,1]' }
        ]
      },
      'trees': {
        title: 'Maximum Depth of Binary Tree',
        description: 'Given the root of a binary tree, return its maximum depth.',
        examples: [
          { input: '[3,9,20,null,null,15,7]', output: '3' },
          { input: '[1,null,2]', output: '2' },
          { input: '[]', output: '0' }
        ],
        starterCode: {
          javascript: `/**\n * Definition for a binary tree node.\n * function TreeNode(val, left, right) {\n *     this.val = (val===undefined ? 0 : val)\n *     this.left = (left===undefined ? null : left)\n *     this.right = (right===undefined ? null : right)\n * }\n */\nfunction maxDepth(root) {\n    // Your code here\n    \n}`,
          python: `# Definition for a binary tree node.\nclass TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef maxDepth(root):\n    # Your code here\n    pass`,
          cpp: `/**\n * Definition for a binary tree node.\n * struct TreeNode {\n *     int val;\n *     TreeNode *left;\n *     TreeNode *right;\n *     TreeNode() : val(0), left(nullptr), right(nullptr) {}\n *     TreeNode(int x) : val(x), left(nullptr), right(nullptr) {}\n *     TreeNode(int x, TreeNode *left, TreeNode *right) : val(x), left(left), right(right) {}\n * };\n */\nclass Solution {\npublic:\n    int maxDepth(TreeNode* root) {\n        // Your code here\n        \n    }\n};`
        },
        testCases: [
          { input: '[3,9,20,null,null,15,7]', expected: '3' },
          { input: '[1,null,2]', expected: '2' },
          { input: '[]', expected: '0' }
        ]
      }
    }

    const defaultProblem = {
      title: `${topic.name} Challenge`,
      description: `Solve this ${topic.name.toLowerCase()} problem to test your skills.`,
      examples: [
        { input: 'Example input', output: 'Example output' }
      ],
      starterCode: {
        javascript: `function solve(input) {\n    // Your code here\n    \n}`,
        python: `def solve(input):\n    # Your code here\n    pass`,
        cpp: `#include <iostream>\n#include <vector>\nusing namespace std;\n\nclass Solution {\npublic:\n    // Your code here\n};\n\nint main() {\n    return 0;\n}`
      },
      testCases: [
        { input: 'test1', expected: 'result1' },
        { input: 'test2', expected: 'result2' }
      ]
    }

    const selectedProblem = problems[topic.id] || defaultProblem

    return {
      id: Date.now(),
      ...selectedProblem,
      difficulty: difficulty,
      topic: topic.id,
      timeLimit: difficulty === 'easy' ? 20 : difficulty === 'medium' ? 35 : 50,
      generatedAt: new Date().toISOString()
    }
  }

  const getDefaultStarterCode = (lang) => {
    const templates = {
      javascript: `function solve(input) {\n    // Your code here\n    \n}`,
      python: `def solve(input):\n    # Your code here\n    pass`,
      cpp: `#include <iostream>\n#include <vector>\nusing namespace std;\n\nclass Solution {\npublic:\n    // Your code here\n};\n\nint main() {\n    return 0;\n}`
    }
    return templates[lang] || templates.javascript
  }

  const getLanguageExtension = (lang) => {
    switch (lang) {
      case 'javascript':
        return [javascript()]
      case 'python':
        return [python()]
      case 'cpp':
        return [cpp()]
      default:
        return [javascript()]
    }
  }

  const runCode = async () => {
    setIsRunning(true)
    setOutput('')
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800))
      
      if (!code || code.trim() === '') {
        setOutput('❌ Error: No code to execute')
        return
      }
      
      // Validate syntax first
      const validation = CodeExecutionService.validateSyntax(code, language)
      
      if (!validation.valid) {
        setOutput(`❌ Compilation Error:\n${validation.errors.join('\n')}`)
        return
      }
      
      setOutput('✅ Code compiled successfully!\n\nRun tests to validate your solution.')
      
    } catch (error) {
      setOutput(`❌ Runtime Error:\n${error.message}`)
    } finally {
      setIsRunning(false)
    }
  }

  const runTests = async () => {
    setIsRunning(true)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      if (!selectedProblem.testCases || selectedProblem.testCases.length === 0) {
        toast.error('No test cases available')
        return
      }
      
      // Execute code with test cases
      const results = CodeExecutionService.executeCode(
        code, 
        language, 
        selectedProblem.testCases
      )
      
      setTestResults(results)
      
      const passedTests = results.filter(r => r.passed).length
      const score = Math.round((passedTests / results.length) * 100)
      
      if (passedTests === results.length) {
        setCompletedProblems(prev => [...prev, selectedProblem.id])
        updateUserStats('coding', score, 100)
        toast.success('All tests passed! 🎉')
        
        // Update progress
        setUserProgress(prev => ({
          ...prev,
          [selectedProblem.topic]: (prev[selectedProblem.topic] || 0) + 1
        }))
      } else {
        toast.error(`${passedTests}/${results.length} tests passed`)
      }
      
    } catch (error) {
      toast.error(`Failed to run tests: ${error.message}`)
      console.error('Test execution error:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const getAIAnalysis = async () => {
    if (!code.trim()) {
      toast.error('Please write some code before requesting analysis')
      return
    }

    setIsAnalyzing(true)
    
    try {
      const analysis = await analyzeCode(code, language, selectedProblem.description)
      setAiAnalysis(analysis)
      toast.success('AI analysis complete!')
    } catch (error) {
      console.error('Error getting AI analysis:', error)
      
      // Fallback analysis
      const fallbackAnalysis = {
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(1)',
        correctness: Math.floor(Math.random() * 30) + 70,
        codeQuality: Math.floor(Math.random() * 25) + 75,
        strengths: [
          'Clean and readable code structure',
          'Good variable naming conventions',
          'Proper error handling'
        ],
        improvements: [
          'Consider edge cases',
          'Add input validation',
          'Optimize for better performance'
        ],
        bugs: [],
        optimizations: [
          'Use more efficient data structures',
          'Reduce redundant operations'
        ],
        overallScore: Math.floor(Math.random() * 20) + 75,
        feedback: 'Good solution! Your approach shows understanding of the problem. Consider optimizing for better time complexity.'
      }
      
      setAiAnalysis(fallbackAnalysis)
      toast.success('AI analysis complete!')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const resetProblem = () => {
    setCode(selectedProblem.starterCode[language] || getDefaultStarterCode(language))
    setOutput('')
    setTestResults([])
    setAiAnalysis(null)
    setShowSolution(false)
    setTimeSpent(0)
    setStartTime(Date.now())
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Problem solving interface
  if (selectedProblem) {
    const passedTests = testResults.filter(r => r.passed).length
    const totalTests = testResults.length

    return (
      <div className="min-h-screen py-8 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <button
                onClick={() => {
                  setSelectedProblem(null)
                  setStartTime(null)
                  setTimeSpent(0)
                }}
                className="text-primary-600 hover:text-primary-700 mb-2 flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Topics</span>
              </button>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {selectedProblem.title}
              </h1>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  selectedProblem.difficulty === 'easy' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' :
                  selectedProblem.difficulty === 'medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300' :
                  'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                }`}>
                  {selectedProblem.difficulty?.charAt(0).toUpperCase() + selectedProblem.difficulty?.slice(1)}
                </span>
                <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
                  <Timer className="w-4 h-4" />
                  <span className="text-sm">{formatTime(timeSpent)}</span>
                </div>
                {completedProblems.includes(selectedProblem.id) && (
                  <div className="flex items-center space-x-1 text-success-600">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">Solved</span>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={language}
                onChange={(e) => {
                  setLanguage(e.target.value)
                  setCode(selectedProblem.starterCode?.[e.target.value] || getDefaultStarterCode(e.target.value))
                }}
                className="input-field py-2"
              >
                <option value="javascript">JavaScript</option>
                <option value="python">Python</option>
                <option value="cpp">C++</option>
              </select>
              <button
                onClick={() => generateRandomProblem(selectedTopic)}
                disabled={isGenerating}
                className="btn-secondary flex items-center space-x-2"
              >
                {isGenerating ? (
                  <Loader className="w-4 h-4 animate-spin" />
                ) : (
                  <Shuffle className="w-4 h-4" />
                )}
                <span>New Problem</span>
              </button>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-6">
            {/* Problem Description */}
            <div className="space-y-6">
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Problem Description
                </h3>
                <p className="text-gray-700 dark:text-gray-300 mb-6">
                  {selectedProblem.description}
                </p>
                
                {selectedProblem.examples && (
                  <>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">Examples:</h4>
                    <div className="space-y-3">
                      {selectedProblem.examples.map((example, index) => {
                        // Helper function to format input/output for display
                        const formatValue = (value) => {
                          if (typeof value === 'string') {
                            return value
                          } else if (typeof value === 'object' && value !== null) {
                            return JSON.stringify(value, null, 2)
                          } else {
                            return String(value)
                          }
                        }

                        return (
                          <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                            <div className="text-sm">
                              <span className="font-medium text-gray-900 dark:text-white">Input:</span>
                              <pre className="ml-2 font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap inline">
                                {formatValue(example.input)}
                              </pre>
                            </div>
                            <div className="text-sm mt-1">
                              <span className="font-medium text-gray-900 dark:text-white">Output:</span>
                              <pre className="ml-2 font-mono text-gray-700 dark:text-gray-300 whitespace-pre-wrap inline">
                                {formatValue(example.output)}
                              </pre>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </>
                )}

                {selectedProblem.constraints && (
                  <>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3 mt-6">Constraints:</h4>
                    <ul className="space-y-1">
                      {selectedProblem.constraints.map((constraint, index) => (
                        <li key={index} className="text-sm text-gray-600 dark:text-gray-400">
                          • {constraint}
                        </li>
                      ))}
                    </ul>
                  </>
                )}
              </div>

              {/* Test Results */}
              {testResults.length > 0 && (
                <div className="card p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      Test Results
                    </h3>
                    <div className="text-sm">
                      <span className={`font-medium ${
                        passedTests === totalTests ? 'text-success-600' : 'text-orange-600'
                      }`}>
                        {passedTests}/{totalTests} Passed
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    {testResults.map((result) => (
                      <div key={result.id} className={`p-3 rounded-lg border ${
                        result.passed 
                          ? 'border-success-200 bg-success-50 dark:bg-success-900/20' 
                          : 'border-red-200 bg-red-50 dark:bg-red-900/20'
                      }`}>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            Test Case {result.id + 1}
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">{result.executionTime}ms</span>
                            {result.passed ? (
                              <CheckCircle className="w-4 h-4 text-success-600" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-600" />
                            )}
                          </div>
                        </div>
                        <div className="text-xs space-y-1">
                          <div>
                            <span className="font-medium">Input:</span>
                            <span className="ml-2 font-mono">{result.input}</span>
                          </div>
                          <div>
                            <span className="font-medium">Expected:</span>
                            <span className="ml-2 font-mono">{result.expected}</span>
                          </div>
                          <div>
                            <span className="font-medium">Actual:</span>
                            <span className="ml-2 font-mono">{result.actual}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* AI Analysis */}
              {aiAnalysis && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    AI Code Analysis
                  </h3>
                  
                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                      <div className="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Time Complexity
                      </div>
                      <div className="font-mono text-blue-700 dark:text-blue-300">
                        {aiAnalysis.timeComplexity}
                      </div>
                    </div>
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
                      <div className="text-sm font-medium text-purple-800 dark:text-purple-200">
                        Space Complexity
                      </div>
                      <div className="font-mono text-purple-700 dark:text-purple-300">
                        {aiAnalysis.spaceComplexity}
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                      <div className="text-sm font-medium text-green-800 dark:text-green-200">
                        Correctness Score
                      </div>
                      <div className="text-2xl font-bold text-green-700 dark:text-green-300">
                        {aiAnalysis.correctness}%
                      </div>
                    </div>
                    <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
                      <div className="text-sm font-medium text-orange-800 dark:text-orange-200">
                        Code Quality
                      </div>
                      <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
                        {aiAnalysis.codeQuality}%
                      </div>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <h4 className="font-medium text-success-700 dark:text-success-300 mb-2">
                        Strengths
                      </h4>
                      <ul className="space-y-1">
                        {aiAnalysis.strengths.map((strength, index) => (
                          <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                            <CheckCircle className="w-3 h-3 text-success-500 mt-0.5 mr-2 flex-shrink-0" />
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-orange-700 dark:text-orange-300 mb-2">
                        Improvements
                      </h4>
                      <ul className="space-y-1">
                        {aiAnalysis.improvements.map((improvement, index) => (
                          <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                            <Lightbulb className="w-3 h-3 text-orange-500 mt-0.5 mr-2 flex-shrink-0" />
                            {improvement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {aiAnalysis.bugs && aiAnalysis.bugs.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-medium text-red-700 dark:text-red-300 mb-2 flex items-center">
                        <Bug className="w-4 h-4 mr-2" />
                        Potential Issues
                      </h4>
                      <ul className="space-y-1">
                        {aiAnalysis.bugs.map((bug, index) => (
                          <li key={index} className="text-sm text-red-600 dark:text-red-400 flex items-start">
                            <XCircle className="w-3 h-3 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                            {bug}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900 dark:text-white">Overall Score</span>
                      <span className="text-2xl font-bold text-primary-600">{aiAnalysis.overallScore}%</span>
                    </div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {aiAnalysis.feedback}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Code Editor */}
            <div className="space-y-6">
              <div className="card p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Code Editor
                  </h3>
                  <div className="flex items-center space-x-2">
                   
                    <button
                      onClick={resetProblem}
                      className="btn-secondary text-sm flex items-center space-x-1"
                    >
                      <RotateCcw className="w-4 h-4" />
                      <span>Reset</span>
                    </button>
                  </div>
                </div>

                <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  <CodeMirror
                    value={showSolution ? (selectedProblem.solution?.[language] || 'Solution not available') : code}
                    height="400px"
                    extensions={getLanguageExtension(language)}
                    theme={isDark ? oneDark : undefined}
                    onChange={(value) => !showSolution && setCode(value)}
                    readOnly={showSolution}
                  />
                </div>

                <div className="flex items-center space-x-3 mt-4">
                  <button
                    onClick={runCode}
                    disabled={isRunning}
                    className="btn-secondary disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isRunning ? (
                      <Loader className="w-4 h-4 animate-spin" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                    <span>Run Code</span>
                  </button>

                  <button
                    onClick={runTests}
                    disabled={isRunning || showSolution}
                    className="btn-primary disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isRunning ? (
                      <Loader className="w-4 h-4 animate-spin" />
                    ) : (
                      <CheckCircle className="w-4 h-4" />
                    )}
                    <span>Run Tests</span>
                  </button>

                  <button
                    onClick={getAIAnalysis}
                    disabled={isAnalyzing || showSolution}
                    className="btn-warning disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isAnalyzing ? (
                      <Loader className="w-4 h-4 animate-spin" />
                    ) : (
                      <Sparkles className="w-4 h-4" />
                    )}
                    <span>AI Analysis</span>
                  </button>
                </div>
              </div>

              {/* Output */}
              {output && (
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Output
                  </h3>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm whitespace-pre-wrap">
                    {output}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Show locked interface if coding section is locked
  if (isLocked) {
    return (
      <div className="min-h-screen py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <div className="w-24 h-24 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Code className="w-12 h-12 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Coding Challenge
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
              Advanced coding practice platform
            </p>
          </div>

          {/* Lock Notice */}
          <div className="card p-8 text-center mb-8">
            <div className="w-16 h-16 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              🚧 Under Development
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 mb-6">
              Our advanced coding challenge platform is currently being enhanced with new features and improvements.
            </p>
            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                🔥 Coming Soon Features:
              </h3>
              <div className="grid md:grid-cols-2 gap-4 text-left">
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Sparkles className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">AI-Powered Problem Generation</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Brain className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Smart Code Analysis & Hints</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Trophy className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Advanced Progress Tracking</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Personalized Learning Paths</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Code className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Multi-Language Support</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Real-time Code Execution</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Collaborative Coding</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Award className="w-4 h-4 text-blue-600" />
                    <span className="text-sm text-blue-800 dark:text-blue-200">Achievement System</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-lg mb-6">
              <h3 className="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-3">
                💡 What You Can Do Now:
              </h3>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">HR Interview Practice</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Master behavioral questions</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Brain className="w-6 h-6 text-green-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Aptitude Tests</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Sharpen logical reasoning</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                    <MessageCircle className="w-6 h-6 text-purple-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">AI Doubt Solver</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Get instant help</p>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-center space-x-4">
              <button
                onClick={() => setIsLocked(false)}
                className="btn-primary flex items-center space-x-2"
              >
                <Zap className="w-4 h-4" />
                <span>Preview Mode (Developer)</span>
              </button>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Expected Launch: Coming Soon
              </div>
            </div>
          </div>

          {/* Preview of Topics */}
          <div className="card p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 text-center">
              📚 Topics We're Preparing
            </h3>
            <div className="grid md:grid-cols-4 gap-4">
              {['Arrays & Strings', 'Linked Lists', 'Trees & Graphs', 'Dynamic Programming', 'Sorting & Searching', 'Hash Tables', 'Stacks & Queues', 'Bit Manipulation'].map((topic, index) => (
                <div key={index} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-center">
                  <div className="text-sm font-medium text-gray-900 dark:text-white mb-1">{topic}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">20+ Problems</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Topic selection interface
  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Data Structures & Algorithms
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
            Master coding interviews with AI-generated problems across all DSA topics
          </p>
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-4 h-4 text-purple-500" />
              <span>AI-Generated Problems</span>
            </div>
            <div className="flex items-center space-x-2">
              <Brain className="w-4 h-4 text-blue-500" />
              <span>Smart Code Analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4 text-yellow-500" />
              <span>Progress Tracking</span>
            </div>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="card p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Your Progress
          </h2>
          <div className="grid md:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {Object.values(userProgress).reduce((sum, count) => sum + count, 0)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Problems Solved
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {Object.keys(userProgress).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Topics Practiced
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {completedProblems.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Total Completed
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-1">
                {Math.round((Object.keys(userProgress).length / dsaTopics.length) * 100)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Overall Progress
              </div>
            </div>
          </div>
        </div>

        {/* DSA Topics Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dsaTopics.map((topic) => (
            <div key={topic.id} className="card card-hover p-6 group">
              <div className={`w-16 h-16 bg-gradient-to-r ${topic.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200`}>
                <topic.icon className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {topic.name}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                {topic.description}
              </p>

              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Difficulty:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {topic.difficulty}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Problems:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {topic.totalProblems}+
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Solved:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {userProgress[topic.id] || 0}
                  </span>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Key Concepts:</h4>
                <div className="flex flex-wrap gap-1">
                  {topic.concepts.slice(0, 3).map((concept, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded-full text-gray-600 dark:text-gray-400">
                      {concept}
                    </span>
                  ))}
                  {topic.concepts.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded-full text-gray-600 dark:text-gray-400">
                      +{topic.concepts.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              <button
                onClick={() => {
                  setSelectedTopic(topic)
                  generateRandomProblem(topic)
                }}
                disabled={isGenerating}
                className={`w-full bg-gradient-to-r ${topic.color} text-white font-medium py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 flex items-center justify-center space-x-2 disabled:opacity-50`}
              >
                {isGenerating ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Code className="w-4 h-4" />
                    <span>Start Practice</span>
                  </>
                )}
              </button>
            </div>
          ))}
        </div>

        {/* Features */}
        <div className="card p-8 mt-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6 text-center">
            Advanced Coding Features
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">AI-Generated Problems</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Unlimited unique problems generated by AI for each topic
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Brain className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Smart Analysis</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                AI analyzes your code for complexity, bugs, and optimizations
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Real-time Testing</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Proper code execution with comprehensive test case validation
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Code className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Multi-Language Support</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                JavaScript, Python, and C++ with proper syntax validation
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CodingChallenge