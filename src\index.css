@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-950 text-white transition-colors duration-300;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-cyan-600 via-blue-600 to-fuchsia-600 hover:from-cyan-500 hover:via-blue-500 hover:to-fuchsia-500 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 ring-1 ring-white/10 shadow-[0_0_20px_rgba(56,189,248,0.3)];
  }
  
  .btn-secondary {
    @apply bg-white/5 hover:bg-white/10 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 ring-1 ring-white/10;
  }
  
  .btn-success {
    @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2;
  }
  
  .btn-warning {
    @apply bg-warning-600 hover:bg-warning-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-warning-500 focus:ring-offset-2;
  }
  
  .card {
    @apply rounded-xl border border-cyan-400/20 bg-gray-900/40 backdrop-blur-md shadow-[0_0_30px_rgba(56,189,248,0.08)] transition-all duration-300;
  }
  
  .card-hover {
    @apply hover:shadow-xl hover:scale-105 cursor-pointer;
  }
  
  .input-field {
    @apply w-full px-4 py-2 rounded-lg bg-white/5 text-white placeholder:text-gray-400 border border-white/10 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-colors duration-300;
  }
  
  .gradient-bg {
    @apply bg-gradient-to-br from-cyan-600 via-blue-600 to-fuchsia-600;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-cyan-400 via-blue-400 to-fuchsia-400 bg-clip-text text-transparent;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-cyan-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-cyan-600;
}

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Voice recording animation */
.recording-pulse {
  animation: recording-pulse 1.5s infinite;
}

@keyframes recording-pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

/* Code editor custom styles */
.cm-editor {
  @apply rounded-lg border border-white/10 bg-black/30;
}

.cm-focused {
  @apply ring-2 ring-cyan-500 border-transparent;
}