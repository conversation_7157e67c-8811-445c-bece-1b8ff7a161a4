{"name": "crackly", "version": "1.0.0", "description": "AI-powered Interview Preparation Hub - Crack interviews with confidence", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite"}, "keywords": ["interview", "preparation", "ai", "coding", "aptitude", "hr"], "author": "Crackly Team", "license": "MIT", "dependencies": {"@codemirror/lang-cpp": "^6.0.3", "@codemirror/lang-javascript": "^6.1.4", "@codemirror/lang-python": "^6.1.3", "@codemirror/theme-one-dark": "^6.1.2", "@google/generative-ai": "^0.24.1", "@uiw/react-codemirror": "^4.19.9", "axios": "^1.11.0", "codemirror": "^6.0.1", "firebase": "^12.0.0", "framer-motion": "^12.23.12", "install": "^0.13.0", "lucide-react": "^0.263.1", "pdfjs-dist": "^5.4.54", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.8.1"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^3.1.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "vite": "^4.2.0"}}