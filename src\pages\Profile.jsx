import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { 
  User, 
  Mail, 
  Edit3, 
  Save, 
  X, 
  Trophy, 
  Target, 
  TrendingUp, 
  Star,
  Mic,
  Brain,
  Code,
  Calendar,
  Award,
  BarChart3,
  Settings
} from 'lucide-react'

const Profile = () => {
  const { currentUser, userData, updateProfile } = useAuth()
  
  // Provide default values if userData is not loaded yet
  const user = userData || {
    displayName: currentUser?.displayName || 'User',
    email: currentUser?.email || '',
    photoURL: currentUser?.photoURL || '',
    stats: {
      hrInterviewsCompleted: 0,
      aptitudeQuizzesTaken: 0,
      codingChallengesSolved: 0,
      averageScore: 0
    },
    achievements: [],
    progress: {
      hrInterview: 0,
      aptitudeQuiz: 0,
      codingChallenge: 0
    }
  }
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    displayName: user.displayName,
    email: user.email,
    photoURL: user.photoURL || ''
  })

  const handleSave = async () => {
    try {
      await updateProfile(editForm)
      setIsEditing(false)
    } catch (error) {
      console.error('Error updating profile:', error)
    }
  }

  const handleCancel = () => {
    setEditForm({
      displayName: user.displayName,
      email: user.email,
      photoURL: user.photoURL || ''
    })
    setIsEditing(false)
  }

  const achievements = [
    { 
      id: 'first-steps', 
      icon: Target, 
      title: 'First Steps', 
      description: 'Complete your first practice session',
      color: 'from-blue-500 to-cyan-500'
    },
    { 
      id: 'improving', 
      icon: TrendingUp, 
      title: 'Improving', 
      description: 'Score above 80% in any category',
      color: 'from-green-500 to-emerald-500'
    },
    { 
      id: 'expert', 
      icon: Star, 
      title: 'Expert', 
      description: 'Complete all practice modules',
      color: 'from-purple-500 to-pink-500'
    },
    { 
      id: 'champion', 
      icon: Trophy, 
      title: 'Champion', 
      description: 'Reach top 10 on leaderboard',
      color: 'from-yellow-500 to-orange-500'
    }
  ]

  const totalSessions = user.stats.hrInterviewsCompleted + user.stats.aptitudeQuizzesTaken + user.stats.codingChallengesSolved
  const overallProgress = Math.round((user.progress.hrInterview + user.progress.aptitudeQuiz + user.progress.codingChallenge) / 3)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Profile</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your account and track your progress</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Profile Information */}
          <div className="lg:col-span-1">
            <div className="card p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Profile Information</h2>
                {!isEditing ? (
                  <button
                    onClick={() => setIsEditing(true)}
                    className="p-2 text-gray-500 hover:text-primary-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                ) : (
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSave}
                      className="p-2 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                    >
                      <Save className="w-4 h-4" />
                    </button>
                    <button
                      onClick={handleCancel}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>

              <div className="text-center mb-6">
                <div className="w-24 h-24 bg-gradient-to-br from-primary-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  {user.photoURL ? (
                    <img src={user.photoURL} alt="Profile" className="w-24 h-24 rounded-full object-cover" />
                  ) : (
                    <User className="w-12 h-12 text-white" />
                  )}
                </div>
                
                {isEditing ? (
                  <div className="space-y-4">
                    <input
                      type="text"
                      value={editForm.displayName}
                      onChange={(e) => setEditForm({ ...editForm, displayName: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Your name"
                    />
                    <input
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Your email"
                      disabled
                    />
                    <input
                      type="url"
                      value={editForm.photoURL}
                      onChange={(e) => setEditForm({ ...editForm, photoURL: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="Avatar URL (optional)"
                    />
                  </div>
                ) : (
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">{user.displayName}</h3>
                    <p className="text-gray-600 dark:text-gray-400 flex items-center justify-center">
                      <Mail className="w-4 h-4 mr-2" />
                      {user.email || 'No email provided'}
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-gray-600 dark:text-gray-400">Member since</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {new Date().toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-gray-600 dark:text-gray-400">Total Sessions</span>
                  <span className="font-medium text-gray-900 dark:text-white">{totalSessions}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <span className="text-gray-600 dark:text-gray-400">Average Score</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {Math.round(user.stats.averageScore)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics and Progress */}
          <div className="lg:col-span-2 space-y-8">
            {/* Statistics Overview */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Statistics Overview
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex items-center">
                      <Mic className="w-8 h-8 text-blue-500 mr-3" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">HR Interviews</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Completed</div>
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">{user.stats.hrInterviewsCompleted}</div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div className="flex items-center">
                      <Brain className="w-8 h-8 text-purple-500 mr-3" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">Aptitude Quizzes</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Taken</div>
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-purple-600">{user.stats.aptitudeQuizzesTaken}</div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center">
                      <Code className="w-8 h-8 text-green-500 mr-3" />
                      <div>
                        <div className="font-semibold text-gray-900 dark:text-white">Coding Challenges</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Solved</div>
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-green-600">{user.stats.codingChallengesSolved}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-br from-primary-50 to-purple-50 dark:from-primary-900/20 dark:to-purple-900/20 rounded-lg">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary-600 mb-2">{overallProgress}%</div>
                      <div className="text-gray-600 dark:text-gray-400">Overall Progress</div>
                    </div>
                  </div>

                  <div className="p-4 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600 mb-2">{user.achievements.length}</div>
                      <div className="text-gray-600 dark:text-gray-400">Achievements Unlocked</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Tracking */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Progress Tracking
              </h2>

              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-900 dark:text-white font-medium">HR Interview</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {Math.round(user.progress.hrInterview)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${user.progress.hrInterview}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-900 dark:text-white font-medium">Aptitude Quiz</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {Math.round(user.progress.aptitudeQuiz)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-purple-500 to-pink-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${user.progress.aptitudeQuiz}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-900 dark:text-white font-medium">Coding Challenge</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {Math.round(user.progress.codingChallenge)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-500"
                      style={{ width: `${user.progress.codingChallenge}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Achievements */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                <Award className="w-5 h-5 mr-2" />
                Achievements
              </h2>

              <div className="grid md:grid-cols-2 gap-4">
                {achievements.map((achievement) => {
                  const isUnlocked = user.achievements.includes(achievement.title)
                  return (
                    <div 
                      key={achievement.id}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                        isUnlocked 
                          ? 'border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20' 
                          : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          isUnlocked 
                            ? `bg-gradient-to-r ${achievement.color}` 
                            : 'bg-gray-300 dark:bg-gray-600'
                        }`}>
                          <achievement.icon className={`w-5 h-5 ${
                            isUnlocked ? 'text-white' : 'text-gray-500'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <h3 className={`font-semibold ${
                            isUnlocked 
                              ? 'text-gray-900 dark:text-white' 
                              : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            {achievement.title}
                          </h3>
                          <p className={`text-sm ${
                            isUnlocked 
                              ? 'text-gray-600 dark:text-gray-300' 
                              : 'text-gray-400 dark:text-gray-500'
                          }`}>
                            {achievement.description}
                          </p>
                          {isUnlocked && (
                            <div className="mt-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                                Unlocked
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Profile