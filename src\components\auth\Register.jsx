// src/pages/auth/Register.jsx
import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { Eye, EyeOff, User, Mail, Lock, UserPlus, Loader } from 'lucide-react'
import { motion } from 'framer-motion'
<<<<<<< HEAD
=======
import { Eye, EyeOff, Mail, Lock, User, UserPlus, Loader, Phone, ArrowRightLeft, Chrome } from 'lucide-react'
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df

const Register = () => {
  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState({})
  const { register, loginWithGoogle } = useAuth()
  const navigate = useNavigate()

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
    if (errors[e.target.name]) {
      setErrors({ ...errors, [e.target.name]: '' })
    }
  }

  const validateForm = () => {
    const newErrors = {}
<<<<<<< HEAD
    if (!formData.displayName.trim()) newErrors.displayName = 'Name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid'
    if (!formData.password) newErrors.password = 'Password is required'
    else if (formData.password.length < 6) newErrors.password = 'At least 6 characters'
    if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords don’t match'
=======

    if (!formData.displayName.trim()) {
      newErrors.displayName = 'Display name is required'
    }

    // Gmail-only validation
    const gmailRegex = /^[A-Za-z0-9._%+-]+@gmail\.com$/
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!gmailRegex.test(formData.email)) {
      newErrors.email = 'Use a valid Gmail address (e.g., <EMAIL>)'
    }

    // Mobile number validation (E.164 basic or 10-digit local)
    const mobileRegex = /^([+][0-9]{10,15}|[0-9]{10})$/
    if (!formData.mobile.trim()) {
      newErrors.mobile = 'Mobile number is required'
    } else if (!mobileRegex.test(formData.mobile)) {
      newErrors.mobile = 'Enter a valid mobile number (10 digits or +countrycode)'
    }

    // Strong password: 8+ chars, upper, lower, digit, special
    const strongPass = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-={}\[\]:;"'`~<>,.?/]).{8,}$/
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (!strongPass.test(formData.password)) {
      newErrors.password = 'Password must be 8+ chars and include upper, lower, digit, and special char'
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!validateForm()) return
    setIsLoading(true)
    try {
      await register(formData.email, formData.password, formData.displayName, formData.mobile)
      navigate('/')
    } catch (error) {
      console.error('Registration error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
<<<<<<< HEAD
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-100 to-indigo-100 dark:from-gray-900 dark:to-gray-800 px-6 py-12">
=======
    <div className="min-h-screen flex items-center justify-center bg-gray-950 py-12 px-4 sm:px-6 lg:px-8">
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="w-full max-w-md p-8 bg-white dark:bg-gray-900 rounded-xl shadow-xl"
      >
<<<<<<< HEAD
        <div className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 flex items-center justify-center">
            <UserPlus className="text-white w-6 h-6" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Create an account</h2>
          <p className="text-sm mt-2 text-gray-600 dark:text-gray-400">
            Already have an account?{' '}
            <Link to="/login" className="text-indigo-600 hover:underline">Login</Link>
          </p>
=======
        <div className="relative">
          <div className="mx-auto h-12 w-12 bg-gradient-to-r from-cyan-600 via-blue-600 to-fuchsia-600 rounded-xl flex items-center justify-center ring-1 ring-white/10 shadow-[0_0_20px_rgba(56,189,248,0.3)]">
            <UserPlus className="h-6 w-6 text-white" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">Create your Prepify account</h2>
          <div className="mt-3 flex justify-center">
            <button onClick={() => navigate('/login')} type="button" className="inline-flex items-center gap-2 text-xs text-white/80 bg-white/10 hover:bg-white/20 ring-1 ring-white/10 px-3 py-1 rounded-lg">
              <ArrowRightLeft className="w-3 h-3" /> Go to Login
            </button>
          </div>
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
        </div>

        <form className="mt-6 space-y-5" onSubmit={handleSubmit}>
          {[
            { id: 'displayName', label: 'Name', icon: User, type: 'text' },
            { id: 'email', label: 'Email', icon: Mail, type: 'email' },
            { id: 'password', label: 'Password', icon: Lock, type: showPassword ? 'text' : 'password' },
            { id: 'confirmPassword', label: 'Confirm Password', icon: Lock, type: showConfirmPassword ? 'text' : 'password' },
          ].map((field) => (
            <div key={field.id}>
              <div className="relative">
                <field.icon className="absolute left-3 top-3 text-gray-400" />
                <input
<<<<<<< HEAD
                  id={field.id}
                  name={field.id}
                  type={field.type}
                  placeholder={field.label}
                  value={formData[field.id]}
=======
                  id="displayName"
                  name="displayName"
                  type="text"
                  autoComplete="name"
                  required
                  className={`appearance-none relative block w-full px-12 py-3 border ${
                    errors.displayName ? 'border-red-500' : 'border-white/10'
                  } placeholder:text-gray-400 text-white bg-white/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent focus:z-10 sm:text-sm`}
                  placeholder="Display name"
                  value={formData.displayName}
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
                  onChange={handleChange}
                  className={`pl-10 pr-10 py-2 w-full rounded-md border ${
                    errors[field.id] ? 'border-red-500' : 'border-gray-300 dark:border-gray-700'
                  } bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                />
                {(field.id === 'password' || field.id === 'confirmPassword') && (
                  <button
                    type="button"
                    onClick={() => field.id === 'password'
                      ? setShowPassword(!showPassword)
                      : setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-2.5 text-gray-400"
                  >
                    {((field.id === 'password' && showPassword) || (field.id === 'confirmPassword' && showConfirmPassword))
                      ? <EyeOff className="h-5 w-5" />
                      : <Eye className="h-5 w-5" />}
                  </button>
                )}
              </div>
              {errors[field.id] && <p className="text-sm text-red-500 mt-1">{errors[field.id]}</p>}
            </div>
          ))}

<<<<<<< HEAD
          <button
            type="submit"
            disabled={isLoading}
            className="w-full flex justify-center items-center gap-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white py-2 rounded-md hover:opacity-90 transition disabled:opacity-50"
          >
            {isLoading ? <Loader className="animate-spin w-5 h-5" /> : <UserPlus className="w-5 h-5" />}
            {isLoading ? 'Creating...' : 'Create Account'}
          </button>
        </form>
=======
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className={`appearance-none relative block w-full px-12 py-3 border ${
                    errors.email ? 'border-red-500' : 'border-white/10'
                  } placeholder:text-gray-400 text-white bg-white/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent focus:z-10 sm:text-sm`}
                  placeholder="Email address"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email}</p>
              )}
            </div>

            <div>
              <label htmlFor="mobile" className="sr-only">
                Mobile number
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="mobile"
                  name="mobile"
                  type="text"
                  autoComplete="tel"
                  required
                  className={`appearance-none relative block w-full px-12 py-3 border ${
                    errors.mobile ? 'border-red-500' : 'border-white/10'
                  } placeholder:text-gray-400 text-white bg-white/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent focus:z-10 sm:text-sm`}
                  placeholder="Mobile number (+91XXXXXXXXXX or 10 digits)"
                  value={formData.mobile}
                  onChange={handleChange}
                />
              </div>
              {errors.mobile && (
                <p className="mt-1 text-sm text-red-400">{errors.mobile}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  className={`appearance-none relative block w-full px-12 py-3 border ${
                    errors.password ? 'border-red-500' : 'border-white/10'
                  } placeholder:text-gray-400 text-white bg-white/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent focus:z-10 sm:text-sm`}
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleChange}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="sr-only">
                Confirm Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  className={`appearance-none relative block w-full px-12 py-3 border ${
                    errors.confirmPassword ? 'border-red-500' : 'border-white/10'
                  } placeholder:text-gray-400 text-white bg-white/5 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent focus:z-10 sm:text-sm`}
                  placeholder="Confirm password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          <div>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 text-sm font-medium rounded-lg text-white btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <Loader className="w-5 h-5 animate-spin" />
              ) : (
                <>
                  <UserPlus className="w-5 h-5 mr-2" />
                  Create Account
                </>
              )}
            </motion.button>
          </div>
        </motion.form>

        <div className="grid grid-cols-1 gap-3">
          <button onClick={async () => { try { await loginWithGoogle() ; navigate('/') } catch (e) {} }} disabled={isLoading} className="w-full btn-secondary flex items-center justify-center gap-2">
            <Chrome className="w-4 h-4" /> Continue with Google
          </button>
        </div>
>>>>>>> dd99a81203c0011dbbaffe9fb683fb06dcd4c7df
      </motion.div>
    </div>
  )
}

export default Register
