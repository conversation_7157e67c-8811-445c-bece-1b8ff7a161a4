import {
  collection,
  doc,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore'
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  updateProfile,
  sendPasswordResetEmail,
  GoogleAuthProvider,
  signInWithPopup,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  updatePassword,
  linkWithPhoneNumber
} from 'firebase/auth'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { auth, db, storage } from '../config/firebase'

// Authentication Services
export const authService = {
  // Register new user and create Firestore profile (stores mobile)
  register: async (email, password, displayName, mobile = '') => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const user = userCredential.user

      // Update profile
      await update<PERSON><PERSON><PERSON>le(user, { displayName })

      // Create user document in Firestore using the user's UID as document ID
      const userRef = doc(db, 'users', user.uid)
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: displayName,
        mobile: mobile || '',
        createdAt: serverTimestamp(),
        stats: {
          hrInterviewsCompleted: 0,
          aptitudeQuizzesTaken: 0,
          codingChallengesSolved: 0,
          totalScore: 0,
          averageScore: 0
        },
        achievements: [],
        progress: {
          hrInterview: 0,
          aptitudeQuiz: 0,
          codingChallenge: 0
        },
        preferences: {
          jobRole: '',
          experience: 0,
          targetCompanies: []
        }
      })

      return user
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Login user
  login: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      return userCredential.user
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Logout user
  logout: async () => {
    try {
      await signOut(auth)
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Reset password (email link)
  resetPassword: async (email) => {
    try {
      await sendPasswordResetEmail(auth, email)
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Google Sign-In
  loginWithGoogle: async () => {
    try {
      const provider = new GoogleAuthProvider()
      const result = await signInWithPopup(auth, provider)
      const user = result.user

      // Ensure user doc exists
      const userRef = doc(db, 'users', user.uid)
      const snapshot = await getDoc(userRef)
      if (!snapshot.exists()) {
        await setDoc(userRef, {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || '',
          photoURL: user.photoURL || '',
          createdAt: serverTimestamp(),
          stats: {
            hrInterviewsCompleted: 0,
            aptitudeQuizzesTaken: 0,
            codingChallengesSolved: 0,
            totalScore: 0,
            averageScore: 0
          },
          achievements: [],
          progress: {
            hrInterview: 0,
            aptitudeQuiz: 0,
            codingChallenge: 0
          },
          preferences: {
            jobRole: '',
            experience: 0,
            targetCompanies: []
          }
        })
      }

      return user
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // reCAPTCHA for phone auth (use container id present in DOM). Prefer 'invisible' for UX.
  getRecaptchaVerifier: (containerId) => {
    return new RecaptchaVerifier(auth, containerId, { size: 'invisible' })
  },

  // Send OTP to a phone number (E.164 format: +1234567890)
  sendPhoneOtp: async (phoneNumber, recaptchaVerifier) => {
    try {
      return await signInWithPhoneNumber(auth, phoneNumber, recaptchaVerifier)
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Confirm OTP code using the confirmationResult from sendPhoneOtp
  confirmPhoneOtp: async (confirmationResult, code) => {
    try {
      const result = await confirmationResult.confirm(code)
      return result.user
    } catch (error) {
      throw new Error('Invalid OTP')
    }
  },

  // Start linking phone to current user (sends OTP and returns confirmation)
  startLinkPhoneToCurrentUser: async (phoneNumber, recaptchaVerifier) => {
    try {
      if (!auth.currentUser) throw new Error('No authenticated user to link phone')
      return await linkWithPhoneNumber(auth.currentUser, phoneNumber, recaptchaVerifier)
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Confirm link using the confirmation returned by startLinkPhoneToCurrentUser
  confirmLinkPhone: async (confirmation, code) => {
    try {
      const result = await confirmation.confirm(code)
      return result.user
    } catch (error) {
      throw new Error('Invalid OTP')
    }
  },

  // Update current user's password (requires auth, e.g., after phone OTP sign-in)
  updatePasswordForCurrentUser: async (newPassword) => {
    try {
      if (!auth.currentUser) throw new Error('No authenticated user')
      await updatePassword(auth.currentUser, newPassword)
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// User Data Services
export const userService = {
  // Get user data
  getUserData: async (uid) => {
    try {
      const userRef = doc(db, 'users', uid)
      const userDoc = await getDoc(userRef)

      if (userDoc.exists()) {
        return { id: userDoc.id, ...userDoc.data() }
      }
      return null
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Update user stats
  updateUserStats: async (uid, category, score, maxScore) => {
    try {
      const userRef = doc(db, 'users', uid)
      const userData = await userService.getUserData(uid)
      if (!userData) throw new Error('User not found')

      const updates = {
        [`stats.${category}Completed`]: increment(1),
        'stats.totalScore': increment(score),
        updatedAt: serverTimestamp()
      }

      // Update progress
      const currentProgress = userData.progress[category] || 0
      const newProgress = Math.max(currentProgress, (score / maxScore) * 100)
      updates[`progress.${category}`] = newProgress

      await updateDoc(userRef, updates)

      // Recalculate average score
      const newUserData = await userService.getUserData(uid)
      const totalAttempts = Object.values(newUserData.stats)
        .filter(key => key.includes('Completed'))
        .reduce((sum, val) => sum + val, 0)

      if (totalAttempts > 0) {
        await updateDoc(userRef, {
          'stats.averageScore': newUserData.stats.totalScore / totalAttempts
        })
      }

    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Update user profile
  updateProfile: async (uid, profileData) => {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        ...profileData,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Add achievement
  addAchievement: async (uid, achievement) => {
    try {
      const userData = await userService.getUserData(uid)
      if (!userData) throw new Error('User not found')

      const userRef = doc(db, 'users', uid)
      const currentAchievements = userData.achievements || []

      if (!currentAchievements.includes(achievement)) {
        await updateDoc(userRef, {
          achievements: [...currentAchievements, achievement],
          updatedAt: serverTimestamp()
        })
      }
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Session Services
export const sessionService = {
  // Save interview session
  saveInterviewSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'interviewSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Save aptitude session
  saveAptitudeSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'aptitudeSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Save coding session
  saveCodingSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'codingSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Get user sessions
  getUserSessions: async (uid, sessionType, limitCount = 10) => {
    try {
      const collectionName = `${sessionType}Sessions`
      const q = query(
        collection(db, collectionName),
        where('uid', '==', uid),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )

      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Leaderboard Services
export const leaderboardService = {
  // Get global leaderboard
  getGlobalLeaderboard: async (category = 'overall', limitCount = 50) => {
    try {
      let orderField = 'stats.averageScore'

      switch (category) {
        case 'hr':
          orderField = 'stats.hrInterviewsCompleted'
          break
        case 'aptitude':
          orderField = 'stats.aptitudeQuizzesTaken'
          break
        case 'coding':
          orderField = 'stats.codingChallengesSolved'
          break
      }

      const q = query(
        collection(db, 'users'),
        orderBy(orderField, 'desc'),
        limit(limitCount)
      )

      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map((doc, index) => ({
        rank: index + 1,
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// File Upload Services
export const uploadService = {
  // Upload profile picture
  uploadProfilePicture: async (uid, file) => {
    try {
      const storageRef = ref(storage, `profilePictures/${uid}`)
      const snapshot = await uploadBytes(storageRef, file)
      const downloadURL = await getDownloadURL(snapshot.ref)

      // Update user profile with new photo URL
      await userService.updateProfile(uid, { photoURL: downloadURL })

      return downloadURL
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Analytics Services
export const analyticsService = {
  // Track user activity
  trackActivity: async (uid, activity, metadata = {}) => {
    try {
      await addDoc(collection(db, 'analytics'), {
        uid,
        activity,
        metadata,
        timestamp: serverTimestamp()
      })
    } catch (error) {
      console.error('Analytics tracking error:', error)
      // Don't throw error for analytics to avoid disrupting user experience
    }
  },

  // Get user analytics
  getUserAnalytics: async (uid, days = 30) => {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const q = query(
        collection(db, 'analytics'),
        where('uid', '==', uid),
        where('timestamp', '>=', startDate),
        orderBy('timestamp', 'desc')
      )

      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}
