import { 
  collection, 
  doc, 
  addDoc, 
  setDoc,
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit,
  serverTimestamp,
  increment
} from 'firebase/firestore'
import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword, 
  signOut, 
  updateProfile,
  sendPasswordResetEmail
} from 'firebase/auth'
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import { auth, db, storage } from '../config/firebase'

// Authentication Services
export const authService = {
  // Register new user
  register: async (email, password, displayName) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)
      const user = userCredential.user
      
      // Update profile
      await updateProfile(user, { displayName })
      
      // Create user document in Firestore using the user's UID as document ID
      const userRef = doc(db, 'users', user.uid)
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: displayName,
        createdAt: serverTimestamp(),
        stats: {
          hrInterviewsCompleted: 0,
          aptitudeQuizzesTaken: 0,
          codingChallengesSolved: 0,
          totalScore: 0,
          averageScore: 0
        },
        achievements: [],
        progress: {
          hrInterview: 0,
          aptitudeQuiz: 0,
          codingChallenge: 0
        },
        preferences: {
          jobRole: '',
          experience: 0,
          targetCompanies: []
        }
      })
      
      return user
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Login user
  login: async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      return userCredential.user
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Logout user
  logout: async () => {
    try {
      await signOut(auth)
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Reset password
  resetPassword: async (email) => {
    try {
      await sendPasswordResetEmail(auth, email)
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// User Data Services
export const userService = {
  // Get user data
  getUserData: async (uid) => {
    try {
      const userRef = doc(db, 'users', uid)
      const userDoc = await getDoc(userRef)
      
      if (userDoc.exists()) {
        return { id: userDoc.id, ...userDoc.data() }
      }
      return null
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Update user stats
  updateUserStats: async (uid, category, score, maxScore) => {
    try {
      const userRef = doc(db, 'users', uid)
      const userData = await userService.getUserData(uid)
      if (!userData) throw new Error('User not found')
      
      const updates = {
        [`stats.${category}Completed`]: increment(1),
        'stats.totalScore': increment(score),
        updatedAt: serverTimestamp()
      }

      // Update progress
      const currentProgress = userData.progress[category] || 0
      const newProgress = Math.max(currentProgress, (score / maxScore) * 100)
      updates[`progress.${category}`] = newProgress

      await updateDoc(userRef, updates)
      
      // Recalculate average score
      const newUserData = await userService.getUserData(uid)
      const totalAttempts = Object.values(newUserData.stats)
        .filter(key => key.includes('Completed'))
        .reduce((sum, val) => sum + val, 0)
      
      if (totalAttempts > 0) {
        await updateDoc(userRef, {
          'stats.averageScore': newUserData.stats.totalScore / totalAttempts
        })
      }

    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Update user profile
  updateProfile: async (uid, profileData) => {
    try {
      const userRef = doc(db, 'users', uid)
      await updateDoc(userRef, {
        ...profileData,
        updatedAt: serverTimestamp()
      })
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Add achievement
  addAchievement: async (uid, achievement) => {
    try {
      const userData = await userService.getUserData(uid)
      if (!userData) throw new Error('User not found')

      const userRef = doc(db, 'users', uid)
      const currentAchievements = userData.achievements || []
      
      if (!currentAchievements.includes(achievement)) {
        await updateDoc(userRef, {
          achievements: [...currentAchievements, achievement],
          updatedAt: serverTimestamp()
        })
      }
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Session Services
export const sessionService = {
  // Save interview session
  saveInterviewSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'interviewSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Save aptitude session
  saveAptitudeSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'aptitudeSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Save coding session
  saveCodingSession: async (uid, sessionData) => {
    try {
      const sessionRef = await addDoc(collection(db, 'codingSessions'), {
        uid,
        ...sessionData,
        createdAt: serverTimestamp()
      })
      return sessionRef.id
    } catch (error) {
      throw new Error(error.message)
    }
  },

  // Get user sessions
  getUserSessions: async (uid, sessionType, limitCount = 10) => {
    try {
      const collectionName = `${sessionType}Sessions`
      const q = query(
        collection(db, collectionName),
        where('uid', '==', uid),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      )
      
      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Leaderboard Services
export const leaderboardService = {
  // Get global leaderboard
  getGlobalLeaderboard: async (category = 'overall', limitCount = 50) => {
    try {
      let orderField = 'stats.averageScore'
      
      switch (category) {
        case 'hr':
          orderField = 'stats.hrInterviewsCompleted'
          break
        case 'aptitude':
          orderField = 'stats.aptitudeQuizzesTaken'
          break
        case 'coding':
          orderField = 'stats.codingChallengesSolved'
          break
      }

      const q = query(
        collection(db, 'users'),
        orderBy(orderField, 'desc'),
        limit(limitCount)
      )
      
      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map((doc, index) => ({
        rank: index + 1,
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// File Upload Services
export const uploadService = {
  // Upload profile picture
  uploadProfilePicture: async (uid, file) => {
    try {
      const storageRef = ref(storage, `profilePictures/${uid}`)
      const snapshot = await uploadBytes(storageRef, file)
      const downloadURL = await getDownloadURL(snapshot.ref)
      
      // Update user profile with new photo URL
      await userService.updateProfile(uid, { photoURL: downloadURL })
      
      return downloadURL
    } catch (error) {
      throw new Error(error.message)
    }
  }
}

// Analytics Services
export const analyticsService = {
  // Track user activity
  trackActivity: async (uid, activity, metadata = {}) => {
    try {
      await addDoc(collection(db, 'analytics'), {
        uid,
        activity,
        metadata,
        timestamp: serverTimestamp()
      })
    } catch (error) {
      console.error('Analytics tracking error:', error)
      // Don't throw error for analytics to avoid disrupting user experience
    }
  },

  // Get user analytics
  getUserAnalytics: async (uid, days = 30) => {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      
      const q = query(
        collection(db, 'analytics'),
        where('uid', '==', uid),
        where('timestamp', '>=', startDate),
        orderBy('timestamp', 'desc')
      )
      
      const querySnapshot = await getDocs(q)
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }))
    } catch (error) {
      throw new Error(error.message)
    }
  }
}