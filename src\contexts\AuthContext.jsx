import React, { createContext, useContext, useState, useEffect } from 'react'
import { onAuthStateChanged } from 'firebase/auth'
import { auth } from '../config/firebase'
import { authService, userService } from '../services/firebase'
import toast from 'react-hot-toast'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null)
  const [userData, setUserData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user)
        try {
          const data = await userService.getUserData(user.uid)
          setUserData(data)
        } catch (error) {
          console.error('Error fetching user data:', error)
          toast.error('Failed to load user data')
        }
      } else {
        setCurrentUser(null)
        setUserData(null)
      }
      setLoading(false)
    })

    return unsubscribe
  }, [])

  const register = async (email, password, displayName, mobile) => {
    try {
      setLoading(true)
      const user = await authService.register(email, password, displayName, mobile)
      toast.success('Account created successfully!')
      return user
    } catch (error) {
      toast.error(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const login = async (email, password) => {
    try {
      setLoading(true)
      const user = await authService.login(email, password)
      toast.success('Logged in successfully!')
      return user
    } catch (error) {
      toast.error(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
      toast.success('Logged out successfully!')
    } catch (error) {
      toast.error(error.message)
      throw error
    }
  }

  const loginWithGoogle = async () => {
    try {
      setLoading(true)
      const user = await authService.loginWithGoogle()
      toast.success('Signed in with Google!')
      return user
    } catch (error) {
      toast.error(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const getRecaptchaVerifier = (containerId) => authService.getRecaptchaVerifier(containerId)
  const sendPhoneOtp = (phone, verifier) => authService.sendPhoneOtp(phone, verifier)
  const confirmPhoneOtp = (confirmation, code) => authService.confirmPhoneOtp(confirmation, code)
  const startLinkPhoneToCurrentUser = (phone, verifier) => authService.startLinkPhoneToCurrentUser(phone, verifier)
  const confirmLinkPhone = (confirmation, code) => authService.confirmLinkPhone(confirmation, code)
  const updatePasswordForCurrentUser = (newPassword) => authService.updatePasswordForCurrentUser(newPassword)

  const resetPassword = async (email) => {
    try {
      await authService.resetPassword(email)
      toast.success('Password reset email sent!')
    } catch (error) {
      toast.error(error.message)
      throw error
    }
  }

  const updateUserStats = async (category, score, maxScore) => {
    if (!currentUser) return
    
    try {
      await userService.updateUserStats(currentUser.uid, category, score, maxScore)
      // Refresh user data
      const updatedData = await userService.getUserData(currentUser.uid)
      setUserData(updatedData)
    } catch (error) {
      console.error('Error updating user stats:', error)
      toast.error('Failed to update stats')
    }
  }

  const updateProfile = async (profileData) => {
    if (!currentUser) return
    
    try {
      await userService.updateProfile(currentUser.uid, profileData)
      // Refresh user data
      const updatedData = await userService.getUserData(currentUser.uid)
      setUserData(updatedData)
      toast.success('Profile updated successfully!')
    } catch (error) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    }
  }

  const addAchievement = async (achievement) => {
    if (!currentUser) return
    
    try {
      await userService.addAchievement(currentUser.uid, achievement)
      // Refresh user data
      const updatedData = await userService.getUserData(currentUser.uid)
      setUserData(updatedData)
      toast.success(`Achievement unlocked: ${achievement}!`)
    } catch (error) {
      console.error('Error adding achievement:', error)
    }
  }

  const value = {
    currentUser,
    userData,
    loading,
    register,
    login,
    logout,
    resetPassword,
    updateUserStats,
    updateProfile,
    addAchievement,
    loginWithGoogle,
    getRecaptchaVerifier,
    sendPhoneOtp,
    confirmPhoneOtp,
    startLinkPhoneToCurrentUser,
    confirmLinkPhone,
    updatePasswordForCurrentUser
  }

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  )
}