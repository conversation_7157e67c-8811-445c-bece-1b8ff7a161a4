import React, { useState, useEffect } from 'react'
import { useUser } from '../contexts/UserContext'

import { generateAptitudeQuestions, evaluateAnswer } from '../services/gemini'
import toast from 'react-hot-toast'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Brain, 
  RotateCcw, 
  Play, 
  Pause,
  AlertCircle,
  TrendingUp,
  Target,
  Award,
  HelpCircle,
  BookOpen,
  Calculator,
  MessageSquare,
  Shuffle,
  Loader,
  Star,
  BarChart3,
  Lightbulb,
  Zap,
  Timer,
  Trophy
} from 'lucide-react'

const AptitudeQuiz = () => {
  const { user, updateUserStats } = useUser()
  const [selectedCategory, setSelectedCategory] = useState(null)
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState(null)
  const [answers, setAnswers] = useState([])
  const [timeLeft, setTimeLeft] = useState(0)
  const [isActive, setIsActive] = useState(false)
  const [quizComplete, setQuizComplete] = useState(false)
  const [score, setScore] = useState(0)
  const [showExplanation, setShowExplanation] = useState(false)
  const [explanation, setExplanation] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [questions, setQuestions] = useState([])
  const [userProgress, setUserProgress] = useState({})
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [aiHint, setAiHint] = useState('')
  const [showHint, setShowHint] = useState(false)

  const categories = [
    {
      id: 'logical',
      name: 'Logical Reasoning',
      description: 'Test your logical thinking, pattern recognition, and analytical skills',
      icon: Brain,
      color: 'from-blue-500 to-cyan-500',
      timeLimit: 600, // 10 minutes
      questionCount: 10,
      topics: ['Pattern Recognition', 'Syllogisms', 'Blood Relations', 'Coding-Decoding', 'Direction Sense']
    },
    {
      id: 'verbal',
      name: 'Verbal Reasoning',
      description: 'Assess your language comprehension, vocabulary, and communication skills',
      icon: MessageSquare,
      color: 'from-purple-500 to-pink-500',
      timeLimit: 480, // 8 minutes
      questionCount: 10,
      topics: ['Synonyms & Antonyms', 'Reading Comprehension', 'Analogies', 'Sentence Correction', 'Para Jumbles']
    },
    {
      id: 'quantitative',
      name: 'Quantitative Aptitude',
      description: 'Test your mathematical and numerical reasoning abilities',
      icon: Calculator,
      color: 'from-green-500 to-emerald-500',
      timeLimit: 720, // 12 minutes
      questionCount: 10,
      topics: ['Arithmetic', 'Algebra', 'Geometry', 'Data Interpretation', 'Probability']
    },
    {
      id: 'data-interpretation',
      name: 'Data Interpretation',
      description: 'Analyze charts, graphs, and tables to extract meaningful insights',
      icon: BarChart3,
      color: 'from-orange-500 to-red-500',
      timeLimit: 900, // 15 minutes
      questionCount: 8,
      topics: ['Bar Charts', 'Line Graphs', 'Pie Charts', 'Tables', 'Mixed Charts']
    }
  ]

  useEffect(() => {
    let interval = null
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(timeLeft => timeLeft - 1)
      }, 1000)
    } else if (timeLeft === 0 && isActive) {
      completeQuiz()
    }
    return () => clearInterval(interval)
  }, [isActive, timeLeft])

  const generateQuestions = async (category) => {
    setIsGenerating(true)
    try {
      const difficulties = ['easy', 'medium', 'hard']
      const randomDifficulty = difficulties[Math.floor(Math.random() * difficulties.length)]
      
      const generatedQuestions = await generateAptitudeQuestions(
        category.id, 
        randomDifficulty, 
        category.questionCount
      )
      
      // Add unique IDs and metadata
      const enhancedQuestions = generatedQuestions.map((q, index) => ({
        ...q,
        id: Date.now() + index,
        generatedAt: new Date().toISOString()
      }))
      
      setQuestions(enhancedQuestions)
      toast.success(`Generated ${enhancedQuestions.length} questions!`)
      return enhancedQuestions
    } catch (error) {
      console.error('Error generating questions:', error)
      toast.error('Failed to generate questions. Using fallback.')
      
      // Fallback questions
      const fallbackQuestions = Array.from({ length: category.questionCount }, (_, index) => ({
        id: Date.now() + index,
        question: `Sample ${category.name} question ${index + 1}`,
        options: ['Option A', 'Option B', 'Option C', 'Option D'],
        correctAnswer: 'A',
        explanation: 'This is a sample explanation for the question.',
        category: category.id,
        difficulty: 'medium',
        timeLimit: 60
      }))
      
      setQuestions(fallbackQuestions)
      return fallbackQuestions
    } finally {
      setIsGenerating(false)
    }
  }

  const startQuiz = async (category) => {
    setSelectedCategory(category)
    const generatedQuestions = await generateQuestions(category)
    
    if (generatedQuestions.length > 0) {
      setTimeLeft(category.timeLimit)
      setIsActive(true)
      setCurrentQuestionIndex(0)
      setAnswers([])
      setQuizComplete(false)
      setScore(0)
      setShowExplanation(false)
      setShowHint(false)
    }
  }

  const selectAnswer = (answerIndex) => {
    setSelectedAnswer(answerIndex)
    setShowHint(false)
  }

  const getAIHint = async () => {
    if (!questions[currentQuestionIndex]) return
    
    setIsAnalyzing(true)
    try {
      const currentQuestion = questions[currentQuestionIndex]
      const hintPrompt = `Provide a helpful hint for this question without giving away the answer: ${currentQuestion.question}`
      
      // Simulate AI hint generation (replace with actual Gemini call)
      setTimeout(() => {
        const hints = [
          "Think about the pattern or relationship between the elements.",
          "Consider the logical sequence or mathematical operation involved.",
          "Look for keywords that might indicate the type of problem.",
          "Break down the problem into smaller, manageable parts.",
          "Consider elimination method for multiple choice questions."
        ]
        
        setAiHint(hints[Math.floor(Math.random() * hints.length)])
        setShowHint(true)
        setIsAnalyzing(false)
      }, 1500)
    } catch (error) {
      console.error('Error getting AI hint:', error)
      toast.error('Failed to get hint')
      setIsAnalyzing(false)
    }
  }

  const nextQuestion = () => {
    const currentQuestion = questions[currentQuestionIndex]
    const isCorrect = selectedAnswer === currentQuestion.correctAnswer
    
    setAnswers(prev => [...prev, {
      questionId: currentQuestion.id,
      selectedAnswer,
      correct: currentQuestion.correctAnswer,
      isCorrect,
      timeSpent: 60 - (timeLeft % 60) // Approximate time spent on question
    }])

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
      setSelectedAnswer(null)
      setShowExplanation(false)
      setShowHint(false)
    } else {
      completeQuiz()
    }
  }

  const completeQuiz = () => {
    setIsActive(false)
    const correctAnswers = answers.filter(answer => answer.isCorrect).length
    const finalScore = Math.round((correctAnswers / questions.length) * 100)
    setScore(finalScore)
    setQuizComplete(true)
    updateUserStats('aptitude', finalScore, 100)
    
    // Update progress
    setUserProgress(prev => ({
      ...prev,
      [selectedCategory.id]: (prev[selectedCategory.id] || 0) + 1
    }))
  }

  const resetQuiz = () => {
    setSelectedCategory(null)
    setCurrentQuestionIndex(0)
    setSelectedAnswer(null)
    setAnswers([])
    setTimeLeft(0)
    setIsActive(false)
    setQuizComplete(false)
    setScore(0)
    setShowExplanation(false)
    setQuestions([])
    setShowHint(false)
  }

  const getExplanation = async (question, userAnswer, correctAnswer) => {
    setShowExplanation(true)
    setIsAnalyzing(true)
    
    try {
      // Use actual AI to generate explanation
      const explanationText = question.explanation || 'Detailed explanation will be provided here.'
      setExplanation(explanationText)
    } catch (error) {
      console.error('Error getting explanation:', error)
      setExplanation('Unable to generate explanation at this time.')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (quizComplete) {
    const correctAnswers = answers.filter(answer => answer.isCorrect).length
    const totalQuestions = questions.length
    const averageTimePerQuestion = answers.reduce((sum, answer) => sum + answer.timeSpent, 0) / answers.length

    return (
      <div className="min-h-screen py-8 px-4 dark:bg-gray-950 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <div className="card p-8 text-center">
            <div className={`w-20 h-20 bg-gradient-to-br ${
              score >= 80 ? 'from-success-500 to-emerald-500' : 
              score >= 60 ? 'from-warning-500 to-orange-500' : 
              'from-red-500 to-pink-500'
            } rounded-full flex items-center justify-center mx-auto mb-6`}>
              {score >= 80 ? (
                <Award className="w-10 h-10 text-white" />
              ) : score >= 60 ? (
                <TrendingUp className="w-10 h-10 text-white" />
              ) : (
                <Target className="w-10 h-10 text-white" />
              )}
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Quiz Complete!
            </h1>
            
            <div className="text-6xl font-bold text-primary-600 mb-4">
              {score}%
            </div>
            
            <p className="text-xl text-gray-600 dark:text-gray-400 mb-8">
              You scored {correctAnswers} out of {totalQuestions} questions correctly
            </p>

            <div className="grid md:grid-cols-4 gap-6 mb-8">
              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <CheckCircle className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {correctAnswers}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Correct Answers
                </div>
              </div>
              
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
                <XCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-red-600 mb-1">
                  {totalQuestions - correctAnswers}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Incorrect Answers
                </div>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                <Clock className="w-8 h-8 text-purple-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {formatTime(selectedCategory.timeLimit - timeLeft)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Time Taken
                </div>
              </div>

              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <Timer className="w-8 h-8 text-green-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {Math.round(averageTimePerQuestion)}s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Avg per Question
                </div>
              </div>
            </div>

            {/* Performance Analysis */}
            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Performance Analysis
              </h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Strengths</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    {score >= 80 && <li>• Excellent problem-solving skills</li>}
                    {score >= 60 && <li>• Good understanding of concepts</li>}
                    <li>• Completed quiz within time limit</li>
                    {averageTimePerQuestion < 45 && <li>• Efficient time management</li>}
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Areas for Improvement</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                    {score < 60 && <li>• Focus on fundamental concepts</li>}
                    {score < 80 && <li>• Practice more problems in this category</li>}
                    {averageTimePerQuestion > 60 && <li>• Work on speed and accuracy</li>}
                    <li>• Review incorrect answers carefully</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="space-y-4 mb-8">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Question Review
              </h3>
              {questions.map((question, index) => {
                const userAnswer = answers[index]
                const isCorrect = userAnswer?.isCorrect
                
                return (
                  <div key={index} className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg text-left">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Q{index + 1}: {question.question}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">{userAnswer?.timeSpent || 0}s</span>
                        {isCorrect ? (
                          <CheckCircle className="w-5 h-5 text-success-500 flex-shrink-0" />
                        ) : (
                          <XCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      Your answer: <span className={`font-medium ${isCorrect ? 'text-success-600' : 'text-red-600'}`}>
                        {question.options[userAnswer?.selectedAnswer]} ({String.fromCharCode(65 + userAnswer?.selectedAnswer)})
                      </span>
                    </div>
                    {!isCorrect && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        Correct answer: <span className="font-medium text-success-600">
                          {question.options[question.correctAnswer]} ({question.correctAnswer})
                        </span>
                      </div>
                    )}
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      {question.explanation}
                    </p>
                  </div>
                )
              })}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={resetQuiz}
                className="btn-primary flex items-center space-x-2"
              >
                <RotateCcw className="w-5 h-5" />
                <span>Try Another Quiz</span>
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="btn-secondary"
              >
                Back to Home
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (selectedCategory && questions.length > 0) {
    const currentQuestion = questions[currentQuestionIndex]
    const progress = ((currentQuestionIndex + 1) / questions.length) * 100

    return (
      <div className="min-h-screen py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {selectedCategory.name}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Question {currentQuestionIndex + 1} of {questions.length}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${
                timeLeft <= 60 ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' :
                'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
              }`}>
                <Clock className="w-4 h-4" />
                <span className="font-mono font-medium">
                  {formatTime(timeLeft)}
                </span>
              </div>
              <button
                onClick={resetQuiz}
                className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                title="Exit quiz"
              >
                <XCircle className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`bg-gradient-to-r ${selectedCategory.color} h-2 rounded-full transition-all duration-300`}
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* Question Card */}
          <div className="card p-8 mb-6">
            <div className="flex items-center justify-between mb-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${selectedCategory.color} text-white`}>
                {currentQuestion.difficulty?.charAt(0).toUpperCase() + currentQuestion.difficulty?.slice(1)}
              </span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={getAIHint}
                  disabled={isAnalyzing || showHint}
                  className="btn-secondary text-sm flex items-center space-x-1"
                >
                  {isAnalyzing ? (
                    <Loader className="w-4 h-4 animate-spin" />
                  ) : (
                    <Lightbulb className="w-4 h-4" />
                  )}
                  <span>Get Hint</span>
                </button>
              </div>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
              {currentQuestion.question}
            </h2>

            {/* AI Hint */}
            {showHint && aiHint && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <div className="flex items-start space-x-3">
                  <Lightbulb className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                      💡 AI Hint:
                    </h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      {aiHint}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => selectAnswer(index)}
                  className={`w-full p-4 text-left rounded-lg border-2 transition-all duration-200 ${
                    selectedAnswer === index
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      selectedAnswer === index
                        ? 'border-primary-500 bg-primary-500'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {selectedAnswer === index && (
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                    <span className="text-gray-900 dark:text-white font-medium">
                      {String.fromCharCode(65 + index)}. {option}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => getExplanation(currentQuestion, selectedAnswer, currentQuestion.correctAnswer)}
              disabled={selectedAnswer === null || isAnalyzing}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isAnalyzing ? (
                <Loader className="w-4 h-4 animate-spin" />
              ) : (
                <HelpCircle className="w-4 h-4" />
              )}
              <span>Explain Answer</span>
            </button>

            <button
              onClick={nextQuestion}
              disabled={selectedAnswer === null}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <span>
                {currentQuestionIndex < questions.length - 1 ? 'Next Question' : 'Complete Quiz'}
              </span>
              <CheckCircle className="w-4 h-4" />
            </button>
          </div>

          {/* Explanation Modal */}
          {showExplanation && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div className="card max-w-2xl w-full p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Answer Explanation
                  </h3>
                  <button
                    onClick={() => setShowExplanation(false)}
                    className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                  >
                    <XCircle className="w-5 h-5 text-gray-500" />
                  </button>
                </div>
                <div className="mb-4">
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    <strong>Question:</strong> {currentQuestion.question}
                  </p>
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    <strong>Correct Answer:</strong> {currentQuestion.options[currentQuestion.correctAnswer]} ({currentQuestion.correctAnswer})
                  </p>
                </div>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                    💡 Explanation:
                  </h4>
                  <p className="text-blue-700 dark:text-blue-300">
                    {explanation || 'Loading explanation...'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Aptitude Assessment
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
            Test your skills across different aptitude categories with AI-generated questions
          </p>
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Zap className="w-4 h-4 text-purple-500" />
              <span>AI-Generated Questions</span>
            </div>
            <div className="flex items-center space-x-2">
              <Brain className="w-4 h-4 text-blue-500" />
              <span>Smart Hints & Analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4 text-yellow-500" />
              <span>Progress Tracking</span>
            </div>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="card p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Your Progress
          </h2>
          <div className="grid md:grid-cols-4 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-1">
                {Object.values(userProgress).reduce((sum, count) => sum + count, 0)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Quizzes Completed
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-1">
                {Object.keys(userProgress).length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Categories Practiced
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-1">
                {Math.round((Object.keys(userProgress).length / categories.length) * 100)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Overall Progress
              </div>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 mb-1">
                85%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Average Score
              </div>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {categories.map((category) => (
            <div key={category.id} className="card card-hover p-6 group">
              <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-200`}>
                <category.icon className="w-8 h-8 text-white" />
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                {category.name}
              </h3>
              
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                {category.description}
              </p>

              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Questions:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {category.questionCount}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Time Limit:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {Math.floor(category.timeLimit / 60)} minutes
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Completed:</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {userProgress[category.id] || 0} times
                  </span>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Topics Covered:</h4>
                <div className="flex flex-wrap gap-1">
                  {category.topics.slice(0, 3).map((topic, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded-full text-gray-600 dark:text-gray-400">
                      {topic}
                    </span>
                  ))}
                  {category.topics.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-xs rounded-full text-gray-600 dark:text-gray-400">
                      +{category.topics.length - 3} more
                    </span>
                  )}
                </div>
              </div>

              <button
                onClick={() => startQuiz(category)}
                disabled={isGenerating}
                className={`w-full bg-gradient-to-r ${category.color} text-white font-medium py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 flex items-center justify-center space-x-2 disabled:opacity-50`}
              >
                {isGenerating ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    <span>Generating...</span>
                  </>
                ) : (
                  <>
                    <Play className="w-4 h-4" />
                    <span>Start Quiz</span>
                  </>
                )}
              </button>
            </div>
          ))}
        </div>

        {/* Instructions */}
        <div className="card p-6 mt-12">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            How to Take the Quiz
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Choose Category</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Select from Logical, Verbal, Quantitative, or Data Interpretation
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Time Management</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Complete all questions within the given time limit
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Get Results</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                View detailed feedback and explanations for each question
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AptitudeQuiz