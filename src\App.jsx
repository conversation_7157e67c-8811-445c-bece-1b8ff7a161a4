import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import Navbar from './components/Navbar'
import Footer from './components/Footer'
import Home from './pages/Home'
import HRInterview from './pages/HRInterview'
import AptitudeQuiz from './pages/AptitudeQuiz'
import CodingChallenge from './pages/CodingChallenge'
import AIDoubtSolver from './pages/AIDoubtSolver'
import ResumeAnalyzer from './pages/ResumeAnalyzer'

import Profile from './pages/Profile'
import Login from './components/auth/Login'
import Register from './components/auth/Register'
import ProtectedRoute from './components/auth/ProtectedRoute'
import GeminiTest from './components/GeminiTest'
import { ThemeProvider } from './contexts/ThemeContext'
import { AuthProvider } from './contexts/AuthContext'
import { UserProvider } from './contexts/UserContext'

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <UserProvider>
          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
            <div className="min-h-screen transition-colors duration-300 bg-gray-950 text-white">
              {/* Futuristic background */}
              <div aria-hidden className="pointer-events-none fixed inset-0 -z-10">
                <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(56,189,248,0.12),rgba(0,0,0,0)_50%)]" />
                <div className="absolute inset-0 bg-[linear-gradient(120deg,rgba(168,85,247,0.05),rgba(59,130,246,0.05),rgba(56,189,248,0.05))]" />
                <div className="absolute inset-0 opacity-[0.06] bg-[url('data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' stroke='%23ffffff' stroke-width='0.5' opacity='0.35'%3E%3Cpath d='M0 50 L100 50'/%3E%3Cpath d='M50 0 L50 100'/%3E%3Ccircle cx='50' cy='50' r='49'/%3E%3C/g%3E%3C/svg%3E')]" />
              </div>
              <Toaster
                position="top-center"
                toastOptions={{
                  duration: 3000,
                  style: {
                    background: 'var(--toast-bg)',
                    color: 'var(--toast-color)',
                  },
                  success: {
                    iconTheme: {
                      primary: 'green',
                      secondary: '#FFFFFF',
                    },
                    style:{
                      backgroundColor:'green',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: 'red',
                      secondary: '#FFFFFF',
                    },
                    style:{
                      backgroundColor:'red'
                    },
                  },
                }}
              />
              <Routes>
                {/* Public Routes */}
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                
                {/* Protected Routes */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <Home />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
                
                <Route path="/hr-interview" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <HRInterview />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
                
                <Route path="/aptitude-quiz" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <AptitudeQuiz />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
                
                {/* <Route path="/coding-challenge" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <CodingChallenge />
                      </main>
                    </div>
                  </ProtectedRoute>
                } /> */}
                
                <Route path="/resume-analyzer" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <ResumeAnalyzer />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />

                <Route path="/ai-doubt-solver" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <AIDoubtSolver />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
                
                
                
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <Profile />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
                
                {/* Test Route - Remove in production */}
                <Route path="/test-gemini" element={
                  <ProtectedRoute>
                    <div>
                      <Navbar />
                      <main className="pt-16">
                        <GeminiTest />
                      </main>
                      <Footer />
                    </div>
                  </ProtectedRoute>
                } />
              </Routes>
            </div>
          </Router>
        </UserProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App