
// Code execution service for validating user solutions
export class CodeExecutionService {
  
  // Execute JavaScript code with test cases
  static executeJavaScript(code, testCases, functionName) {
    const results = []
    
    try {
      // Create a safe execution environment
      const func = new Function('return ' + code)()
      
      if (typeof func !== 'function') {
        throw new Error('Code must define a function')
      }
      
      for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i]
        try {
          const startTime = performance.now()
          
          // Parse input if it's a string representation
          let input = testCase.input
          if (typeof input === 'string') {
            try {
              input = JSON.parse(input)
            } catch (e) {
              // If parsing fails, use as string
            }
          }
          
          // Execute the function
          let result
          if (Array.isArray(input)) {
            result = func(...input)
          } else {
            result = func(input)
          }
          
          const endTime = performance.now()
          const executionTime = Math.round(endTime - startTime)
          
          // Convert result to string for comparison
          const actualOutput = JSON.stringify(result)
          const expectedOutput = typeof testCase.expected === 'string' 
            ? testCase.expected 
            : JSON.stringify(testCase.expected)
          
          const passed = actualOutput === expectedOutput
          
          results.push({
            id: i,
            input: typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input),
            expected: expectedOutput,
            actual: actualOutput,
            passed,
            executionTime,
            error: null
          })
          
        } catch (error) {
          results.push({
            id: i,
            input: typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input),
            expected: typeof testCase.expected === 'string' ? testCase.expected : JSON.stringify(testCase.expected),
            actual: `Error: ${error.message}`,
            passed: false,
            executionTime: 0,
            error: error.message
          })
        }
      }
      
    } catch (error) {
      // If code compilation fails
      for (let i = 0; i < testCases.length; i++) {
        results.push({
          id: i,
          input: typeof testCases[i].input === 'string' ? testCases[i].input : JSON.stringify(testCases[i].input),
          expected: typeof testCases[i].expected === 'string' ? testCases[i].expected : JSON.stringify(testCases[i].expected),
          actual: `Compilation Error: ${error.message}`,
          passed: false,
          executionTime: 0,
          error: error.message
        })
      }
    }
    
    return results
  }
  
  // Validate Python code syntax (simulation)
  static validatePython(code) {
    const errors = []
    
    // Basic Python syntax checks
    if (!code.includes('def ')) {
      errors.push('Python code must define a function')
    }
    
    // Check for basic syntax issues
    const lines = code.split('\n')
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line === '') continue
      
      // Check indentation (basic)
      if (line.startsWith('def ') && !line.endsWith(':')) {
        errors.push(`Line ${i + 1}: Function definition must end with ':'`)
      }
      
      // Check for return statement
      if (line.includes('return') && !line.startsWith('return') && !line.includes('    return')) {
        // This is a basic check, real Python would be more complex
      }
    }
    
    return errors
  }
  
  // Simulate Python execution (since we can't actually run Python in browser)
  static simulatePython(code, testCases) {
    const results = []
    const errors = this.validatePython(code)
    
    if (errors.length > 0) {
      for (let i = 0; i < testCases.length; i++) {
        results.push({
          id: i,
          input: typeof testCases[i].input === 'string' ? testCases[i].input : JSON.stringify(testCases[i].input),
          expected: typeof testCases[i].expected === 'string' ? testCases[i].expected : JSON.stringify(testCases[i].expected),
          actual: `Syntax Error: ${errors[0]}`,
          passed: false,
          executionTime: 0,
          error: errors[0]
        })
      }
      return results
    }
    
    // Simulate execution with some logic
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i]
      
      // Simulate execution time
      const executionTime = Math.floor(Math.random() * 50) + 10
      
      // For demo purposes, we'll simulate some test cases passing based on code quality
      const codeQuality = this.assessCodeQuality(code)
      const passed = Math.random() < codeQuality
      
      const actualOutput = passed 
        ? (typeof testCase.expected === 'string' ? testCase.expected : JSON.stringify(testCase.expected))
        : 'Incorrect output'
      
      results.push({
        id: i,
        input: typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input),
        expected: typeof testCase.expected === 'string' ? testCase.expected : JSON.stringify(testCase.expected),
        actual: actualOutput,
        passed,
        executionTime,
        error: null
      })
    }
    
    return results
  }
  
  // Validate C++ code syntax (simulation)
  static validateCpp(code) {
    const errors = []
    
    // Basic C++ syntax checks
    if (!code.includes('#include')) {
      errors.push('C++ code should include necessary headers')
    }
    
    if (!code.includes('int main') && !code.includes('class Solution')) {
      errors.push('C++ code must have a main function or Solution class')
    }
    
    // Check for basic syntax
    const openBraces = (code.match(/{/g) || []).length
    const closeBraces = (code.match(/}/g) || []).length
    
    if (openBraces !== closeBraces) {
      errors.push('Mismatched braces in C++ code')
    }
    
    return errors
  }
  
  // Simulate C++ execution
  static simulateCpp(code, testCases) {
    const results = []
    const errors = this.validateCpp(code)
    
    if (errors.length > 0) {
      for (let i = 0; i < testCases.length; i++) {
        results.push({
          id: i,
          input: typeof testCases[i].input === 'string' ? testCases[i].input : JSON.stringify(testCases[i].input),
          expected: typeof testCases[i].expected === 'string' ? testCases[i].expected : JSON.stringify(testCases[i].expected),
          actual: `Compilation Error: ${errors[0]}`,
          passed: false,
          executionTime: 0,
          error: errors[0]
        })
      }
      return results
    }
    
    // Simulate execution
    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i]
      const executionTime = Math.floor(Math.random() * 30) + 5
      
      const codeQuality = this.assessCodeQuality(code)
      const passed = Math.random() < codeQuality
      
      const actualOutput = passed 
        ? (typeof testCase.expected === 'string' ? testCase.expected : JSON.stringify(testCase.expected))
        : 'Incorrect output'
      
      results.push({
        id: i,
        input: typeof testCase.input === 'string' ? testCase.input : JSON.stringify(testCase.input),
        expected: typeof testCase.expected === 'string' ? testCase.expected : JSON.stringify(testCase.expected),
        actual: actualOutput,
        passed,
        executionTime,
        error: null
      })
    }
    
    return results
  }
  
  // Assess code quality for simulation purposes
  static assessCodeQuality(code) {
    let quality = 0.5 // Base quality
    
    // Increase quality based on code characteristics
    if (code.length > 50) quality += 0.1
    if (code.includes('return')) quality += 0.1
    if (code.includes('for') || code.includes('while')) quality += 0.1
    if (code.includes('if')) quality += 0.1
    if (code.includes('function') || code.includes('def') || code.includes('class')) quality += 0.1
    
    // Decrease quality for obvious issues
    if (code.includes('hello') || code.includes('test')) quality -= 0.3
    if (code.trim().length < 20) quality -= 0.2
    
    return Math.max(0.1, Math.min(0.9, quality))
  }
  
  // Main execution method
  static executeCode(code, language, testCases, functionName = null) {
    if (!code || !code.trim()) {
      throw new Error('No code provided')
    }
    
    if (!testCases || testCases.length === 0) {
      throw new Error('No test cases provided')
    }
    
    switch (language) {
      case 'javascript':
        return this.executeJavaScript(code, testCases, functionName)
      case 'python':
        return this.simulatePython(code, testCases)
      case 'cpp':
        return this.simulateCpp(code, testCases)
      default:
        throw new Error(`Unsupported language: ${language}`)
    }
  }
  
  // Validate code syntax
  static validateSyntax(code, language) {
    switch (language) {
      case 'javascript':
        try {
          new Function(code)
          return { valid: true, errors: [] }
        } catch (error) {
          return { valid: false, errors: [error.message] }
        }
      case 'python':
        const pythonErrors = this.validatePython(code)
        return { valid: pythonErrors.length === 0, errors: pythonErrors }
      case 'cpp':
        const cppErrors = this.validateCpp(code)
        return { valid: cppErrors.length === 0, errors: cppErrors }
      default:
        return { valid: false, errors: [`Unsupported language: ${language}`] }
    }
  }
}